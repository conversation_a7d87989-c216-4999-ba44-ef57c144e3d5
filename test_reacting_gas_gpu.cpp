#include <iostream>
#include <chrono>
#include <field/gas.h>
#include <cosystem/cosystem.h>
#include <field/visc/laminar.h>

using namespace std;
using namespace std::chrono;

int main() {
    cout << "Testing Reacting Gas GPU Implementation" << endl;
    
    // Create coordinate system and viscosity model
    cCosystem *coo = new cCosystem();
    coo->setnx(3);
    coo->setnvel(3);
    
    cLaminar *visc = new cLaminar(coo);
    
    // Create reacting gas model
    cMfReactingGas *gas = new cMfReactingGas(coo, visc);
    
    // Test parameters
    const int nq = 1000;  // Number of grid points
    const int nv = gas->getnv();
    const int naux = gas->getnaux();
    
    cout << "Number of variables: " << nv << endl;
    cout << "Number of auxiliary variables: " << naux << endl;
    cout << "Number of grid points: " << nq << endl;
    
    // Allocate memory for test data
    Real *q = new Real[nv * nq];
    Real *aux = new Real[naux * nq];
    Real *dU = new Real[nv * nq];
    Real *dq = new Real[nv * nq];
    
    // Initialize test data with realistic values
    for(int iq = 0; iq < nq; iq++) {
        // Velocity components
        q[ADDR(0, iq, nq)] = 100.0;  // u
        q[ADDR(1, iq, nq)] = 50.0;   // v
        q[ADDR(2, iq, nq)] = 0.0;    // w
        q[ADDR(3, iq, nq)] = 300.0;  // temperature
        q[ADDR(4, iq, nq)] = 101325.0; // pressure
        q[ADDR(5, iq, nq)] = 0.5;    // mixture fraction
        
        // Initialize perturbations
        dU[ADDR(0, iq, nq)] = 0.1;
        dU[ADDR(1, iq, nq)] = 0.05;
        dU[ADDR(2, iq, nq)] = 0.0;
        dU[ADDR(3, iq, nq)] = 0.0;
        dU[ADDR(4, iq, nq)] = 100.0;
        dU[ADDR(5, iq, nq)] = 0.01;
    }
    
    cout << "\nTesting GPU auxiliary variables calculation..." << endl;
    
    // Test GPU auxv3 method
    auto start = high_resolution_clock::now();
    gas->auxv3gpu(0, nq, q, aux, nq);
    auto end = high_resolution_clock::now();
    auto gpu_auxv_time = duration_cast<microseconds>(end - start);
    
    cout << "GPU auxv3 completed in " << gpu_auxv_time.count() << " microseconds" << endl;
    
    // Print some results
    cout << "Sample auxiliary variables (first point):" << endl;
    cout << "  Density: " << aux[ADDR(0, 0, nq)] << endl;
    cout << "  Kinetic energy: " << aux[ADDR(1, 0, nq)] << endl;
    cout << "  Speed of sound: " << aux[ADDR(2, 0, nq)] << endl;
    cout << "  Total enthalpy: " << aux[ADDR(3, 0, nq)] << endl;
    
    cout << "\nTesting GPU variable derivatives calculation..." << endl;
    
    // Test GPU dvar3 method
    start = high_resolution_clock::now();
    gas->dvar3gpu(0, nq, q, aux, dU, dq, nq);
    end = high_resolution_clock::now();
    auto gpu_dvar_time = duration_cast<microseconds>(end - start);
    
    cout << "GPU dvar3 completed in " << gpu_dvar_time.count() << " microseconds" << endl;
    
    // Print some results
    cout << "Sample variable derivatives (first point):" << endl;
    cout << "  du: " << dq[ADDR(0, 0, nq)] << endl;
    cout << "  dv: " << dq[ADDR(1, 0, nq)] << endl;
    cout << "  dw: " << dq[ADDR(2, 0, nq)] << endl;
    cout << "  dT: " << dq[ADDR(3, 0, nq)] << endl;
    cout << "  dp: " << dq[ADDR(4, 0, nq)] << endl;
    cout << "  dz: " << dq[ADDR(5, 0, nq)] << endl;
    
    // Performance comparison note
    cout << "\nGPU Implementation Summary:" << endl;
    cout << "- auxv3gpu time: " << gpu_auxv_time.count() << " μs" << endl;
    cout << "- dvar3gpu time: " << gpu_dvar_time.count() << " μs" << endl;
    cout << "- Total GPU time: " << (gpu_auxv_time + gpu_dvar_time).count() << " μs" << endl;
    
    // Cleanup
    delete[] q;
    delete[] aux;
    delete[] dU;
    delete[] dq;
    delete gas;
    delete visc;
    delete coo;
    
    cout << "\nTest completed successfully!" << endl;
    return 0;
}
