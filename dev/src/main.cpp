
   using namespace std;
//#include <fenv.h>
#  include <cse.h>
#  include <dev.h>
#ifdef PETSC
#  include <petscksp.h>
#endif
static char petsc_help[] = "hello world\n";

   int main( int argc, char **argv )
  {

      cCase    *cse=NULL;
      cEnv     *env;
      cDevice  *dev;
      string    arg,marg,carg;
#ifdef PETSC
      PetscErrorCode ierr;
#endif

//      feenableexcept(FE_INVALID | FE_OVERFLOW);

      env= new cEnv(&argc,&argv);

      marg= env->getarg( "-m" );
      carg= env->getarg( "-c" );

      dev= new cDevice( NULL );

      if( marg != "" && carg != "" )
     {
         cload( env,&cse  );
         read_input( "input.au3x", marg, dev, cse );

//         if( cse )
//        {
//            cutil( env,cse );
//        }

         dev->create_case_folders( carg );

         if(env->getarg( "-d" )!="")
        {
            cout << "code version: gpu 2025.8.19" << endl;
            partition( env,cse,dev );
            preprocess( env,cse,dev );
            dev->save_dev();
            csave( env,cse );
        }

         arg= env->getarg( "-r" );
         if( arg != "" )
        {
            dev->load_dev();
            compute( env,cse,dev );
        }

         arg= env->getarg( "-t" );
         if( arg != "" )
        {
            dev->load_dev();
            postprocess( env,cse,dev );
        }

         arg= env->getarg( "-u" );
         if( arg != "" )
        {
            upostprocess( env,cse,dev );
        }

     }

      delete cse; cse=NULL;
      delete dev; dev=NULL;
      delete env; env=NULL;
  }

//   int main( int argc, char **argv )
//  {
//
//      cCase    *cse=NULL;
//      cEnv     *env;
//      cDevice  *dsrv,*dios,*dftn,*dev;
//      string    arg,marg,carg;
//#ifdef PETSC
//      PetscErrorCode ierr;
//#endif
//
//
//      env= new cEnv(&argc,&argv);
//
//      marg= env->getarg( "-m" );
//      carg= env->getarg( "-c" );
//
//      dsrv= new cDevice( NULL );
//      dios= new cBinioDevice( dsrv ); ((cBinioDevice*)dios)->extension( ".dev" );
//      dftn= new cDevice( dios );
//
//      if( marg != "" && carg != "" )
//     {
//
//         cload( env,&cse  );
//         dev= NULL;
//         dload( env, dftn, &dev );
//
//         dios->detach(); 
//
//         if( cse )
//        {
//            cutil( env,cse );
//            csave( env,cse );
//        }
//
//         dutil( env,dftn,dev );
//         dev->assigncase( cse );
//
//#ifdef PETSC
//         ierr = PetscInitialize(&argc,&argv,(char*)0,petsc_help);
//         CHKERRQ(ierr);
//         if (ierr) return ierr;
//#endif
//         arg= env->getarg( "-d" );
//         if( arg != "" )
//        {
//            partition( env,cse,dev );
//            csave( env,cse );
//        }
//
//         arg= env->getarg( "-p" );
//         if( arg != "" )
//        {
//            preprocess( env,cse,dev );
//        }
//
//         arg= env->getarg( "-r" );
//         if( arg != "" )
//        {
//            compute( env,cse,dev );
//        }
//
//         arg= env->getarg( "-t" );
//         if( arg != "" )
//        {
//            postprocess( env,cse,dev );
//        }
//
//         arg= env->getarg( "-u" );
//         if( arg != "" )
//        {
//            upostprocess( env,cse,dev );
//        }
//
//         arg= env->getarg( "-j" );
//         if( arg != "" )
//        {
//            outputjl09( env,cse,dev );
//        }
//
//
//         dsrv->attach( dios );
//         arg= env->getarg( "-r" );
//         if( arg == "" )
//        {
//            dsave( env,dftn,dev );
//        }
//   
//         delete cse; cse=NULL;
//     }
//
//      delete dsrv; dsrv= NULL;dios= NULL; dftn=NULL;
////    stophere();
//      delete env; env=NULL;
//
//  }
