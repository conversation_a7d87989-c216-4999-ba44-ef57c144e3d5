#  ifndef _FBNDRY_
#  define _FBNDRY_

#  include <pickle/proto.h>
#  include <field/gas.h>
#  include <paral.h>
#  include <geo/kdtree.h>

#  define MXNFRE 30


   enum fbndry_t { neut_fbndry, free_fbndry, inv_fbndry, visc_fbndry, inj_fbndry, wave_fbndry, slide_fbndry, 
                   free_fbndry_wake, free_fbndry_x, free_fbndry_subin, free_fbndry_subout, thermal_wall_fbndry, free_fbndry_massflow };

   class cFbndry: public cPickle
  {
      protected:
         pthread_mutex_t mtx;
         cCosystem *coo;
         cGas      *fld;
         void lock();
         void unlock();
         Int        nfre_send, nfre_reci;
         Int        nfre_send_lin, lin_dft[MXNFRE];
         Real       send_lin_wavnum[MXNFRE];
         string     bndtag;
         cAu3xView<Int> NULL_iview;
         cAu3xView<Real> NULL_rview;

      public:
         cFbndry();
         virtual ~cFbndry();
         virtual fbndry_t gettype(){ return neut_fbndry; };
         virtual void field( cCosystem *c, cGas *f ){ coo=c; fld=f; };
         //virtual void  bcs( Int , Int , Real, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                                   Int *[], Real *[], Real *[], Real *[], Real **[],
         //                                                   Real *[], Real *[], Real *[] ){};
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                           Int *siqbq, Real *sxq, Real *sq, Real *saux,
                           Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb){};
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb){};
         virtual void bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[], 
                             Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[], Int ifre, Real ibpa ){};
         virtual void bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                             Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[] ) {};


         virtual void auxv( Int ibs, Int ibe, Real *qb[], Real *auxb[] ){};
         virtual void auxv( Int ibs, Int ibe, Real *sqb, Real *sauxb, Int nbb ){};
         virtual void auxv( Int ibs, Int ibe, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, string arch ){};

         //virtual void iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *rhs[], Real *wnb[], Real *wxdb[], Real *auxfb[]  ){};
         virtual void iflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, Int *sibq, Real *sxq, Real *sq, Real *saux, Real *srhs, Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq ){};
         virtual void iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb, cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb ){};

         virtual void dsflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zcb_re[], Real *zcb_im[], Real *zb_re[], Real *zb_im[], Real *rhsb[],
                             Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *zc_re[], Real *zc_im[], Real *z_re[], Real *z_im[], Real *rhs[], Real *xc[],
                             Real *wn[], Real *wxdc[]  ) {};



         //virtual void mflx( Int , Int , Real *[], Real *[], Real *[], Real *[], Int *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[]  ){};
         virtual  void mflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb,     
                                   Int *sibq,  Real *sxq, Real *sq,  Real *saux,  Real *srhs,
                                               Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  ){};

         virtual  void mflx( Int ibs, Int ibe,     cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& rhs,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb ){};


         virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],  
                                   Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  ){};
         //virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
         //                          Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],  
         //                          Real *wnb[], Real *wxdb[], Real *auxfb[] ){};
         virtual void ilhs( Int ibs, Int ibe,   Real *sxb, Real *sqb, Real *sauxb, Real *slhsb,
                                     Int *sibq, Real *sxq, Real  *sq, Real *saux,  Real *slhsa,     
                                    Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq ){};
         virtual void ilhs( Int ibs, Int ibe,     cAu3xView<Real>& xb,   cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& lhsb,
                            cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,   cAu3xView<Real>& sq, cAu3xView<Real>& aux,  cAu3xView<Real>& lhsa,
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb ){};

         //virtual void grad( Int , Int , Real *[], Real *[], Int *[], Real *[], Real *[], 
         //                   Int *[], Real *[], Real **[], Real *[], Real *[] ){};
         virtual void grad( Int ibs, Int ibe, Real *sxqb, Real *sqb, Int *siqbq, Real *sxq, Real *sq,      
                            Real *sdxdx, Real *sdqdx, Real *swnb, Real *swxdb, Int nq, Int nbb ){};
         virtual void grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[],           
                              Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] ) {};

//         virtual void diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
//                                Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[],  Real *res[],
//                                            Real *wnb[], Real *wxdb[], Real *auxfb[]  ){};
         virtual void diflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                    Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux,  Real *sres,
                                               Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  ){};
         virtual void diflx( Int ibs, Int ibe,     cAu3xView<Real> &xb,  cAu3xView<Real>& sqb,   cAu3xView<Real>& sauxb,  cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real> &xq,  cAu3xView<Real>& sq,    cAu3xView<Real>& saux,   cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& swxdb, cAu3xView<Real>& sauxfb ){};

         //virtual void dmflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
         //                        Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
         //                                   Real *wnb[], Real *wxdb[], Real *auxfb[]  ){};
         virtual void dmflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                    Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux, Real *sres,
                                               Real *swnb,Real *swxdb, Real *sauxfb, Int nbb, Int ng  ){};
         virtual void dmflx( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb,cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb ){};


         virtual void dmflx_z( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zb_re[], Real *zb_im[], Real *resb_re[], Real *resb_im[],
                               Int *ibq[],       Real *xq[], Real *q[],  Real *aux[],  Real *z_re[],  Real *z_im[],  Real *res_re[],  Real *res_im[],
                               Real *wnb[], Real *wxdb[], Real *auxfb[]  ){};

         virtual void request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **x );
         virtual void request( Int ibs, Int ibe, Real tm, Real *sxb, Int nbb, Int *n, Int *m, Int *l, Real **sx ){};
         virtual  void service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **qr, 
                        Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                        Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix );
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               Real *sxq, Real  *sq, Real  *saux, Real *sdxdx, Real *sdqdx, Int ibs, Int ibe,
                               Int *sibq, Real *sxb, Real *sqb, Real *sauxb, Int nq, Int nbb ){};
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               cAu3xView<Real>& xq, cAu3xView<Real>&  q, cAu3xView<Real>& aux, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, Int ibs, Int ibe,  
                               cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb ){};
         virtual bool layers(){ return false; };
         //virtual void maverage( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
         //                       Int, cParal *par, bool bposix, string path, string bnm, bool bwrite ){};
         virtual void maverage( Int ibs,Int ibe, cAu3xView<Int>& ibq, cAu3xView<Int>& ibql, cAu3xView<Real>& wnb, cAu3xView<Real>& xb, cAu3xView<Real>& q,
                                cAu3xView<Real>& aux, Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite ){};

         virtual void maverage( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                                Int nfre, Real **zc_re[20], Real **zc_im[20], Real **z_re[20], Real **z_im[20],
                                Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite ){};
         virtual void bdflux( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                              Int nfre, Real **z_re[20], Real **z_im[20],
                              Int mbl, cParal *par ){};


         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *q[], Real *w, Real *qb[] ){};
         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *sq, Real *w, cAu3xView<Real>& qb ){};

         virtual void accept_z( Int ibs, Int ibe, Int nvr, Int nq, Real *q[], Real *w, Real *qb[], Int ifre0, Real *zb_re[], Real *zb_im[] ){};


         virtual void fft( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                           cParal *par, Int mfre_nrbc ) {};
         virtual void fftlin( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real **z_re[],
                              Real **z_im[], Real *ibpa, cParal *par ) {};


         //virtual void nrbc( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb[], Int *iqbq[], Real *q[], Real *wnb[],
         //                   Real *wxdb[], Int *ibql, string bnm ) {};
         virtual void nrbc( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb[], Int *iqbq[], Real *q[], Real *wnb[],
                            Real *wxdb[], Int *ibql, Int mfre_nrbc, string bnm, Real omega ) {};
         virtual void nrbc_z( Int ibs, Int ibe, Real *z_re[], Real *z_im[], Int *iqbq[], Real *xb[],
                              Real *qb0_re[], Real *qb0_im[], Real *qb_re[], Real *qb_im[],Real *wnb[],
                              Int *ibql, string bnm, Real omega, Real freq, Real ibpa, cParal *par ) {};

         virtual Int getnfre_send() { return nfre_send; };
         virtual Int getnfre_reci() { return nfre_reci; };
         virtual void setnfre_send( Int nvar ) { nfre_send = nvar; };
         virtual void setnfre_reci( Int nvar ) { nfre_reci = nvar; };

         virtual void set_nfre_send_lin( Int nvar ) { nfre_send_lin = nvar; };
         virtual Int get_nfre_send_lin() { return nfre_send_lin; };
         virtual void set_lin_dft( Int *tmp ) 
        {  
            for(Int i=0; i<nfre_send_lin; i++)
           {
               lin_dft[i] = tmp[i];
           } 
        };
         virtual void get_fre_send_lin( Int *tmp ) 
        {  
            for(Int i=0; i<nfre_send_lin; i++)
           {
               tmp[i] = lin_dft[i];
           } 
        };

         virtual void set_send_lin_wavnum(Int *tmp )
        {
            for(Int i=0; i<nfre_send_lin; i++)
           {
               send_lin_wavnum[i] = tmp[i];
           } 
        }

         virtual void report_zsetup() {};

         void setbndtag(string var) {bndtag = var;};

         virtual void set_avg_type( string ivar ) {};

  };

   class cFreeFbndry: public cFbndry
  {
      protected:
         Int               nbl, nsl[1000];
         Real             *sxbvg,**xbvg;
         Real             *sqbvg,**qbvg;
         Real             *sqbav,**qbav;
         Real             *swbvg,**wbvg;
         Real             *sdflux,**dflux;
         Real             *smixvar, *mixvar[20];

         Real *sqwrk;cAu3xView<Real> qwrk;
         Real *sxwrk;cAu3xView<Real> xwrk;


         Real             *szfft_re[1000],**zfft_re[1000];
         Real             *szfft_im[1000],**zfft_im[1000];
         Real             *szfftlin_re[MXNFRE],**zfftlin_re[MXNFRE];
         Real             *szfftlin_im[MXNFRE],**zfftlin_im[MXNFRE];
         Real             *szfftlin_nrbc_re[1000],**zfftlin_nrbc_re[1000];
         Real             *szfftlin_nrbc_im[1000],**zfftlin_nrbc_im[1000];
//         Real             **c2s_re, **c2s_im, **c4s_re, **c4s_im, **c5s_re, **c5s_im;
         complex<Real>   *c1s[1000], *c2s[1000], *c3s[1000], *c4s[1000], *c5s[1000];
         Int              linmode[1000];
         //assume maximum 1000 cells in the pitchwise direction, should be enough

         void extract_lin_nrbc_mode( Int ibs, Int ibe, Real *qb0_re[], Real *qb0_im[], Real *z_re[],
                                     Real *z_im[], Int *iqbq[], Real *xb[], Real *wnb[], Int *ibql,
                                     string bnm, Real ibpa, cParal *par );

         string avg_type; //mo: mixed out, area: avre averaging

      public:
         cFreeFbndry();
         virtual ~cFreeFbndry();
         virtual fbndry_t gettype(){ return free_fbndry; };
         //virtual void  bcs( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                                   Int *[], Real *[], Real *[], Real *[], Real **[],
         //                                                   Real *[], Real *[], Real *[] );
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                           Int *siqbq, Real *sxq, Real *sq, Real *saux,
                           Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb);
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
         virtual void bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[], 
                             Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[], Int ifre, Real ibpa );
         virtual void bcs_z( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb0_re[], Real *qb0_im[], Real *xqb[], Real *qb_re[],
                             Real *qb_im[], Int *iqbq[], Real *xq[], Real *wnb[] );
         virtual void auxv( Int ibs, Int ibe, Real *qb[], Real *auxb[] );
         virtual void auxv( Int ibs, Int ibe, Real *sqb, Real *sauxb, Int nbb );
         virtual void auxv( Int ibs, Int ibe, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, string arch );

         //virtual void iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *rhs[], Real *wnb[], Real *wxdb[], Real *auxfb[]  );
         virtual void iflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, Int *sibq, Real *sxq, Real *sq, Real *saux, Real *srhs, Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  );
         virtual void iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb, cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& rhs, 
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );

         virtual void dsflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zcb_re[], Real *zcb_im[], Real *zb_re[], Real *zb_im[], Real *rhsb[],
                             Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *zc_re[], Real *zc_im[], Real *z_re[], Real *z_im[], Real *rhs[], Real *xc[],
                             Real *wn[], Real *wxdc[]  );

         virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
                                   Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  );
         //virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
         //                          Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
         //                          Real *wnb[], Real *wxdb[], Real *auxfb[] );
         virtual void ilhs( Int ibs, Int ibe,   Real *sxb, Real *sqb, Real *sauxb, Real *slhsb,
                                     Int *sibq, Real *sxq, Real  *sq, Real *saux,  Real *slhsa,     
                                    Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq );
         virtual void ilhs( Int ibs, Int ibe,     cAu3xView<Real>& xb,   cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& lhsb,
                            cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,   cAu3xView<Real>& sq, cAu3xView<Real>& aux,  cAu3xView<Real>& lhsa,
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );
         //virtual void grad( Int , Int , Real *[], Real *[], Int *[], Real *[], Real *[], 
         //                   Int *[], Real *[], Real **[], Real *[], Real *[] );
         virtual void grad( Int ibs, Int ibe, Real *sxqb, Real *sqb, Int *siqbq, Real *sxq, Real *sq,      
                            Real *sdxdx, Real *sdqdx, Real *swnb, Real *swxdb, Int nq, Int nbb );
         virtual void grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[],           
                              Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] );
         //virtual void diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
         //                       Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[],  Real *res[],
         //                                   Real *wnb[], Real *wxdb[], Real *auxfb[]  );
         virtual void diflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                    Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux,  Real *sres,
                                               Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  );
         virtual void diflx( Int ibs, Int ibe,     cAu3xView<Real> &xb,  cAu3xView<Real>& sqb,   cAu3xView<Real>& sauxb,  cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real> &xq,  cAu3xView<Real>& sq,    cAu3xView<Real>& saux,   cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& swxdb, cAu3xView<Real>& sauxfb );
         virtual void request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **x );
         virtual void request( Int ibs, Int ibe, Real tm, Real *sxb, Int nbb, Int *n, Int *m, Int *l, Real **sx );
         virtual  void service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **qr, 
                        Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                        Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix );
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               Real *sxq, Real  *sq, Real  *saux, Real *sdxdx, Real *sdqdx, Int ibs, Int ibe,
                               Int *sibq, Real *sxb, Real *sqb, Real *sauxb, Int nq, Int nbb );
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               cAu3xView<Real>& xq, cAu3xView<Real>&  q, cAu3xView<Real>& aux, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, Int ibs, Int ibe,  
                               cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb );
         virtual bool layers(){ return true; };
         //virtual void maverage( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
         //                       Int, cParal *par, bool bposix, string path, string bnm, bool bwrite );
         virtual void maverage( Int ibs,Int ibe, cAu3xView<Int>& ibq, cAu3xView<Int>& ibql, cAu3xView<Real>& wnb, cAu3xView<Real>& xb, cAu3xView<Real>& q,
                                cAu3xView<Real>& aux, Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite );
         virtual void maverage( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                                Int nfre, Real **zc_re[20], Real **zc_im[20], Real **z_re[20], Real **z_im[20],
                                Int mbl, cParal *par, bool bposix, string path, string bnm, bool bwrite );
         virtual void bdflux( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                              Int nfre, Real **z_re[20], Real **z_im[20],
                              Int mbl, cParal *par );
         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *q[], Real *w, Real *qb[] );
         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *sq, Real *w, cAu3xView<Real>& qb );
         virtual void accept_z( Int ibs, Int ibe, Int nvr, Int nq, Real *q[], Real *w, Real *qb[], Int ifre0, Real *zb_re[], Real *zb_im[] );

         virtual void fft( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real *q[],Real *aux[],
                           cParal *par, Int mfre_nrbc );
         virtual void fftlin( Int ibs,Int ibe, Int *ibq[], Int *ibql, Real *wnb[], Real *xb[], Real **z_re[],
                              Real **z_im[], Real *ibpa, cParal *par );


         virtual void nrbc( Int ibs, Int ibe, Real *xb[], Real *qb0[], Real *qb[], Int *iqbq[], Real *q[], Real *wnb[],
                            Real *wxdb[], Int *ibql, Int mfre_nrbc, string bnm, Real omega );
         virtual void nrbc_z( Int ibs, Int ibe, Real *z_re[], Real *z_im[], Int *iqbq[], Real *xb[],
                              Real *qb0_re[], Real *qb0_im[], Real *qb_re[], Real *qb_im[],Real *wnb[],
                              Int *ibql, string bnm, Real omega, Real freq, Real ibpa, cParal *par );

         virtual void report_zsetup();

         virtual void set_avg_type( string svar ) { avg_type = svar; };

  };

   class cFreeFbndrySubin: public cFreeFbndry
  {
      protected:
      public:
         cFreeFbndrySubin() {};
         virtual ~cFreeFbndrySubin() {};
         virtual fbndry_t gettype(){ return free_fbndry_subin; };
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
  };

   class cFreeFbndrySubout: public cFreeFbndry
  {
      protected:
      public:
         cFreeFbndrySubout() {};
         virtual ~cFreeFbndrySubout() {};
         virtual fbndry_t gettype(){ return free_fbndry_subout; };
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
  };

   class cFreeFbndryMass: public cFreeFbndry
  {
      protected:
      public:
         cFreeFbndryMass() {};
         virtual ~cFreeFbndryMass() {};
         virtual fbndry_t gettype(){ return free_fbndry_massflow; };
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
  };

   class cFreeFbndryX: public cFreeFbndry
  {
      protected:
         Real             *wakevar[5];
      public:
         cFreeFbndryX() ;
         virtual ~cFreeFbndryX() ;
         virtual fbndry_t gettype(){ return free_fbndry_x; };
         virtual  void service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **qr, 
                        Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                        Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix );
  };

   class cWaveFbndry: public cFreeFbndry
  {
      protected:

      public:
         cWaveFbndry();
         virtual ~cWaveFbndry();
         virtual bool layers(){ return false; };
         virtual fbndry_t gettype(){ return wave_fbndry; };
         //virtual void  bcs( Int , Int , Real, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                                   Int *[], Real *[], Real *[], Real *[], Real **[],
         //                                                   Real *[], Real *[], Real *[] );
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                           Int *siqbq, Real *sxq, Real *sq, Real *saux,
                           Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb);
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
  };

   class cInvFbndry: public cFbndry
  {
      protected:
      public:
         cInvFbndry();
         virtual ~cInvFbndry();
         virtual fbndry_t gettype(){ return inv_fbndry; };
         //virtual void  bcs( Int , Int , Real, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                                   Int *[], Real *[], Real *[], Real *[], Real **[],
         //                                                   Real *[], Real *[], Real *[] );
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           Real *sxb, Real *sqb0, Real *sauxb0, Real *sxqb, Real *sqb, Real *sauxb,
                           Int *siqbq, Real *sxq, Real *sq, Real *saux,
                           Real *sdqdx, Real *swnb, Real *swxdb , Real *sauxfb, Int nq, Int nbb);
         virtual void bcs( Int ibs, Int ibe, Real tm,
                           cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                           cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                           cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb , cAu3xView<Real>& auxfb);
         //virtual void iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *rhs[], Real *wnb[], Real *wxdb[], Real *auxfb[]  );
         virtual void iflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb, Int *sibq, Real *sxq, Real *sq, Real *saux, Real *srhs, Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq );
         virtual void iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb, cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& rhs, 
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );

         virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
                                   Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
                                   Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  );
         //virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[], 
         //                          Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[], 
         //                          Real *wnb[], Real *wxdb[], Real *auxfb[] );
         virtual void ilhs( Int ibs, Int ibe,   Real *sxb, Real *sqb, Real *sauxb, Real *slhsb,
                                     Int *sibq, Real *sxq, Real  *sq, Real *saux,  Real *slhsa,     
                                    Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq );
         virtual void ilhs( Int ibs, Int ibe,     cAu3xView<Real>& xb,   cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& lhsb,
                            cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,   cAu3xView<Real>& sq, cAu3xView<Real>& aux,  cAu3xView<Real>& lhsa,
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );

         //virtual void grad( Int , Int , Real *[], Real *[], Int *[], Real *[], Real *[], 
         //                   Int *[], Real *[], Real **[], Real *[], Real *[] );
         virtual void grad( Int ibs, Int ibe, Real *sxqb, Real *sqb, Int *siqbq, Real *sxq, Real *sq,      
                            Real *sdxdx, Real *sdqdx, Real *swnb, Real *swxdb, Int nq, Int nbb );
         virtual void grad_z( Int ibs, Int ibe, Real *xqb[], Int *iqbq[], Real *xq[], Int *ijdx[],           
                              Real *dxdx[], Real **dqdx_re[], Real **dqdx_im[], Real *wnb[], Real *wxdb[] );

         //virtual void diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
         //                       Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[],  Real *res[],
         //                                   Real *wnb[], Real *wxdb[], Real *auxfb[]  );
         virtual void diflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                    Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux,  Real *sres,
                                               Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  );
         virtual void diflx( Int ibs, Int ibe,     cAu3xView<Real> &xb,  cAu3xView<Real>& sqb,   cAu3xView<Real>& sauxb,  cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real> &xq,  cAu3xView<Real>& sq,    cAu3xView<Real>& saux,   cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& swxdb, cAu3xView<Real>& sauxfb );
         virtual void dsflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zcb_re[], Real *zcb_im[], Real *zb_re[], Real *zb_im[], Real *rhsb[],
                             Int *ibq[], Real *xq[], Real *q[], Real *aux[], Real *zc_re[], Real *zc_im[], Real *z_re[], Real *z_im[], Real *rhs[], Real *xc[],
                             Real *wn[], Real *wxdc[]  ) ;
  };

   class cViscFbndry: public cInvFbndry
  {
      protected:
      public:
         cViscFbndry();
         virtual ~cViscFbndry();
         virtual fbndry_t gettype(){ return visc_fbndry; };
         //virtual void  mflx( Int , Int , Real *[], Real *[], Real *[], Real *[], Int *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], Real *[]  );
         virtual  void mflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *srhsb,
                                   Int *sibq,  Real *sxq, Real *sq,  Real *saux,  Real *srhs,
                                               Real *swnb, Real *swxdb, Real *sauxfb, Int nbb, Int nq  );

         virtual  void mflx( Int ibs, Int ibe,     cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& rhs,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );

         //virtual void dmflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
         //                        Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
         //                                   Real *wnb[], Real *wxdb[], Real *auxfb[]  );
         virtual void dmflx( Int ibs, Int ibe, Real *sxb, Real *sqb, Real *sauxb, Real *sdqb, Real *sdauxb, Real *sresb,
                                    Int *sibq, Real *sxq, Real *sq,  Real *saux,  Real *sdq,  Real *sdaux, Real *sres,
                                               Real *swnb,Real *swxdb, Real *sauxfb, Int nbb, Int ng  );
         virtual void dmflx( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb,cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );
         virtual void dmflx_z( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *zb_re[], Real *zb_im[], Real *resb_re[], Real *resb_im[],
                               Int *ibq[],       Real *xq[], Real *q[],  Real *aux[],  Real *z_re[],  Real *z_im[],  Real *res_re[],  Real *res_im[],
                               Real *wnb[], Real *wxdb[], Real *auxfb[]  );

  };

   class cThermalWallFbndry: public cInvFbndry
  {
      protected:
      public:
         cThermalWallFbndry();
         virtual ~cThermalWallFbndry();
         virtual fbndry_t gettype(){ return thermal_wall_fbndry; };

         virtual void iflx( Int ibs, Int ibe, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb, cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& rhs, 
                            cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb ) {};
         virtual void diflx( Int ibs, Int ibe,     cAu3xView<Real> &xb,  cAu3xView<Real>& sqb,   cAu3xView<Real>& sauxb,  cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real> &xq,  cAu3xView<Real>& sq,    cAu3xView<Real>& saux,   cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& swxdb, cAu3xView<Real>& sauxfb ){};
         virtual  void mflx( Int ibs, Int ibe,     cAu3xView<Real>& xb,  cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& rhsb,
                             cAu3xView<Int>& ibq,  cAu3xView<Real>& xq,  cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& rhs,
                             cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );
         virtual void dmflx( Int ibs, Int ibe,    cAu3xView<Real>& xb, cAu3xView<Real>& qb,   cAu3xView<Real>& auxb, cAu3xView<Real>& dqb, cAu3xView<Real>& dauxb, cAu3xView<Real>& resb,
                             cAu3xView<Int>& ibq, cAu3xView<Real>& xq, cAu3xView<Real>& q,    cAu3xView<Real>& aux,  cAu3xView<Real>& dq,  cAu3xView<Real>& daux,  cAu3xView<Real>& res,
                             cAu3xView<Real>& wnb,cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb );
  };

//   class cInjFbndry: public cFbndry
//  {
//       protected:
//
//       public:
//          cInjFbndry();
//          virtual ~cInjFbndry();
//
//          virtual fbndry_t gettype(){ return inj_fbndry; };
//   virtual void bcs( Int ibs, Int ibe, Real t, Real *xb[], Real *qb0[], Real *auxb0[], Real *xqb[], Real *qb[], Real *auxb[], 
//                          Int *iqbq[], Real *xq[], Real *q[], Real *aux[], Real **dqdx[], 
//                                      Real *wnb[], Real *wxdb[] , Real *auxfb[]);
//
//         virtual void auxv( Int ibs, Int ibe, Real *qb[], Real *auxb[] );
//
//   virtual void iflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *rhsb[], 
//                            Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *rhs[],
//                                       Real *wnb[], Real *wxdb[], Real *auxfb[]  );
//
//
//   virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
//                               Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],
//                                          Real *wnb[], Real *wxdb[], Real *auxfb[], cJacBlk *jac_df[2]  );
//   virtual void ilhs( Int ibs, Int ibe,    Real *xb[], Real *qb[], Real *auxb[], Real *lhsb[],
//                               Int *ibq[], Real *xq[], Real  *q[],  Real *aux[], Real *lhs[],
//                                          Real *wnb[], Real *wxdb[], Real *auxfb[] );
//
//   virtual void grad( Int ibs, Int ibe, Real *xqb[], Real *qb[], Int *iqbq[], Real *xq[], Real *q[], 
//                          Int *ijdx[], Real *dxdx[], Real **dqdx[], Real *wnb[], Real *wxdb[] );
//
//   virtual void diflx( Int ibs, Int ibe, Real *xb[], Real *qb[], Real *auxb[], Real *dqb[], Real *dauxb[], Real *resb[],
//                             Int *ibq[], Real *xq[], Real *q[],  Real *aux[],  Real *dq[],  Real *daux[], Real *res[],
//                                            Real *wnb[], Real *wxdb[], Real *auxfb[]  );
//  };

   class cSlideFbndry: public cFreeFbndry
  {
      protected:
         cKdTree        *kdt;
         Int            *idt, *isec;
         Real            ptch;
      public:
         cSlideFbndry();
         virtual ~cSlideFbndry();
         virtual fbndry_t gettype(){ return slide_fbndry; };
         virtual void request( Int ibs, Int ibe, Real tm, Real *xb[], Int *n, Int *m, Int *l, Real **x );
         virtual void request( Int ibs, Int ibe, Real tm, Real *sxb, Int nbb, Int *n, Int *m, Int *l, Real **sx );
         virtual  void service( Int nxr, Int nvr, Int nqr, Real tm, Real *xr[], Real **qr, 
                        Int iqs, Int iqe, Real *xq[], Real  *q[], Real  *aux[], Real *dxdx[], Real **dqdx[],
                        Int ibs, Int ibe, Int *ibq[], Real *xb[], Real *qb[], Real *auxb[], bool bposix );
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               Real *sxq, Real  *sq, Real  *saux, Real *sdxdx, Real *sdqdx, Int ibs, Int ibe,
                               Int *sibq, Real *sxb, Real *sqb, Real *sauxb, Int nq, Int nbb );
         virtual void service( Int nxr, Int nvr, Int nqr, Real tm, Real *sxr, Real **sqr, Int iqs, Int iqe,
                               cAu3xView<Real>& xq, cAu3xView<Real>&  q, cAu3xView<Real>& aux, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, Int ibs, Int ibe,  
                               cAu3xView<Int>& ibq, cAu3xView<Real>& xb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb );
         virtual bool layers(){ return false; };
         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *q[], Real *w, Real *qb[] );
         virtual void accept( Int ibs, Int ibe, Int nv, Int nq, Real tm, Real *sq, Real *w, cAu3xView<Real>& qb );

  };

   cFbndry *newfbndry( Int );

#pragma acc routine seq
   inline void getrf( Int iprm[3][3], Real *a )
  {
      Int             i,j,k;
      Int             n = 3;
// factorisation

      for( i=0;i<n;i++ )
     {
         for( k=i+1;k<n;k++ )
        {
            a[iprm[i][k]]= a[iprm[i][k]]/a[iprm[i][i]];
        } 
         for( j=i+1;j<n;j++ )
        {
            for( k=i+1;k<n;k++ )
           {
               a[iprm[j][k]]-= a[iprm[j][i]]*a[iprm[i][k]];
           }
        }
     }
     
  }

#pragma acc routine seq
   inline void getrs( Int iprm[3][3], Real *a, Real *b )
  {
      Int             i,j;
      Int             n = 3;

// forward sweep

      for( j=0;j<n;j++ )
     {
         for( i=j+1;i<n;i++ )
        {
            b[i]-= a[iprm[j][i]]*b[j];
        }
     }

// backward sweep

      for( j=n-1;j>=0;j-- )
     {
         b[j]/= a[iprm[j][j]];
         for( i=0;i<j;i++ )
        {
            b[i]-= a[iprm[j][i]]*b[j];
        }
     }
  }

#  endif
