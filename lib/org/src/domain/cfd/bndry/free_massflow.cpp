
   using namespace std;

#  include <iomanip>
#  include <domain/cfd/bndry/bndry.h>

   void cFreeFbndryMass::bcs( Int ibs, Int ibe, Real tm,
                              cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                              cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
                              cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
  {
      Int ix,ib,iq,iv,nx,nv,naux;
      Real w;

      Int nbb, nq;

      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
      Int *siqbq;
      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;
 
      Real rhol, unl, vr[3], unr, wn[4], pr, ar, al, pl, tl;

      Real gam = 1.4;
      Real R = 287./10000.;

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      nbb = iqbq.get_dim0();
      nq  = q.get_dim1();
  
      sxb    = xb.get_data();
      sqb0   = qb0.get_data();
      sauxb0 = auxb0.get_data();
      sxqb   = xqb.get_data();
      sqb    = qb.get_data();
      sauxb  = auxb.get_data();
      siqbq  = iqbq.get_data();
      sxq    = xq.get_data();
      sq     = q.get_data();
      saux   = aux.get_data();
      sdqdx  = dqdx.get_data();
      swnb   = wnb.get_data();
      swxdb  = wxdb.get_data();
      sauxfb = auxb.get_data();

      nv= fld->getnv();
      nx= coo->getnx();
      naux= fld->getnaux();

      if( ibe > ibs )
     {
        #pragma acc enter data copyin(this) 
        #pragma acc parallel loop gang vector \
         private( wn, vr)\
         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq], \
                 swnb[0:(nx+1)*nbb],this ) \
         default(none)
         for( ib=ibs;ib<ibe;ib++ )
        {
//blazak book, 3rd, p270
            //iq= iqbq[0][ib];
            iq= siqbq[ib];

            wn[0] = swnb[ADDR(0,ib,nbb)];
            wn[1] = swnb[ADDR(1,ib,nbb)];
            wn[2] = swnb[ADDR(2,ib,nbb)];

            //user input
            tl = sqb0[ADDR(3,ib,nbb)];
            unl = sqb0[ADDR(0,ib,nbb)];

            vr[0] = unl*wn[0];
            vr[1] = unl*wn[1];
            vr[2] = unl*wn[2];

            pr = sq[ADDR(4,iq,nq)];

            sqb[ADDR(0,ib,nbb)]=vr[0];
            sqb[ADDR(1,ib,nbb)]=vr[1];
            sqb[ADDR(2,ib,nbb)]=vr[2];
            sqb[ADDR(3,ib,nbb)]=tl; 
            sqb[ADDR(4,ib,nbb)]=pr;
            for(iv=5; iv<nv; iv++)
           {
               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
           }

//            for(iv=0; iv<nv; iv++)
//           {
//               cout << sqb[ADDR(iv,ib,nbb)] << " ";
//           }
//            cout << "\n";

            w= 0; 
            for( ix=0;ix<nx;ix++ )
           {
               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
           }
            for( ix=0;ix<nx;ix++ )
           {
               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
           }
        }
        #pragma acc exit data copyout(this) 
     }
  }

//   void cFreeFbndryMass::bcs( Int ibs, Int ibe, Real tm,
//                              cAu3xView<Real>& xb, cAu3xView<Real>& qb0, cAu3xView<Real>& auxb0, cAu3xView<Real>& xqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
//                              cAu3xView<Int>&  iqbq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux,
//                              cAu3xView<Real>& dqdx, cAu3xView<Real>& wnb, cAu3xView<Real>& wxdb, cAu3xView<Real>& auxfb )
//  {
//      Int ix,ib,iq,iv,nx,nv,naux;
//      Real w;
//
//      Int nbb, nq;
//
//      Real *sxb, *sqb0, *sauxb0, *sxqb, *sqb, *sauxb;
//      Int *siqbq;
//      Real *sxq, *sq, *saux, *sdqdx, *swnb, *swxdb, *sauxfb;
// 
//      Real rhol, unl, vl[3], vr[3], unr, wn[4], pr, ar, Riemann, al, pl, tl;
//
//      Real gam = 1.4;
//      Real R = 287./10000.;
//
//      nv= fld->getnv();
//      nx= coo->getnx();
//      naux= fld->getnaux();
//
//      nbb = iqbq.get_dim0();
//      nq  = q.get_dim1();
//  
//      sxb    = xb.get_data();
//      sqb0   = qb0.get_data();
//      sauxb0 = auxb0.get_data();
//      sxqb   = xqb.get_data();
//      sqb    = qb.get_data();
//      sauxb  = auxb.get_data();
//      siqbq  = iqbq.get_data();
//      sxq    = xq.get_data();
//      sq     = q.get_data();
//      saux   = aux.get_data();
//      sdqdx  = dqdx.get_data();
//      swnb   = wnb.get_data();
//      swxdb  = wxdb.get_data();
//      sauxfb = auxb.get_data();
//
//      nv= fld->getnv();
//      nx= coo->getnx();
//      naux= fld->getnaux();
//
//      if( ibe > ibs )
//     {
//        #pragma acc enter data copyin(this) 
//        #pragma acc parallel loop gang vector \
//         private(n_free_stream, loc, norm, v_r, v_l)\
//         present(sxb[0:nx*nbb],sqb0[0:nv*nbb],sauxb0[0:naux*nbb],sxqb[0:nx*nbb],sqb[0:nv*nbb],sauxb[0:naux*nbb],siqbq[0:nbb],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq], \
//                 swnb[0:(nx+1)*nbb],swxdb[0:nbb],this ) \
//         default(none)
//         for( ib=ibs;ib<ibe;ib++ )
//        {
//            //iq= iqbq[0][ib];
//            iq= siqbq[ib];
//
//            wn[0] = swnb[ADDR(0,ib,nbb)];
//            wn[1] = swnb[ADDR(1,ib,nbb)];
//            wn[2] = swnb[ADDR(2,ib,nbb)];
//
//            //user input
//            rhol = sqb0[ADDR(3,ib,nbb)];
//            unl = sqb0[ADDR(0,ib,nbb)];
//
//            vl[0] = unl*wn[0];
//            vl[1] = unl*wn[1];
//            vl[2] = unl*wn[2];
//
//            vr[0] = sq[ADDR(0,iq,nq)];
//            vr[1] = sq[ADDR(1,iq,nq)];
//            vr[2] = sq[ADDR(2,iq,nq)];
//
//            unr = vr[0]*wn[0] + vr[1]*wn[1] + vr[2]*wn[2];
//
//            pr = sq[ADDR(4,iq,nq)];
//            ar = saux[ADDR(2,iq,nq)]; 
//   
//            //riemann invariants from the interior, leaving the domain 
//            Riemann = unr + 2*ar/(gam-1.);
//
//            //extrapolate to the ghost cell and find speed of sound
//            al = (Riemann - unl)*(gam-1.)*0.5;
//            al = fmax(al,0.0);
//          
//            pl = rhol*al*al/gam;
//            tl = pl/(R*rhol);
// 
//            sqb[ADDR(0,ib,nbb)]=vl[0];
//            sqb[ADDR(1,ib,nbb)]=vl[1];
//            sqb[ADDR(2,ib,nbb)]=vl[2];
//            sqb[ADDR(3,ib,nbb)]=tl; 
//            sqb[ADDR(4,ib,nbb)]=pl;
//            for(iv=5; iv<nv; iv++)
//           {
//               sqb[ADDR(iv,ib,nbb)]= sqb0[ADDR(iv,ib,nbb)];
//           }
//
////            for(iv=0; iv<nv; iv++)
////           {
////               cout << sqb[ADDR(iv,ib,nbb)] << " ";
////           }
////            cout << "\n";
//
//            w= 0; 
//            for( ix=0;ix<nx;ix++ )
//           {
//               //w+= ( xb[ix][ib]- xq[ix][iq] )*wnb[ix][ib];
//               w+= ( sxb[ADDR(ix,ib,nbb)]- sxq[ADDR(ix,iq,nq)] )*swnb[ADDR(ix,ib,nbb)];
//           }
//            for( ix=0;ix<nx;ix++ )
//           {
//               //xqb[ix][ib]= xq[ix][iq]+ 2*w*wnb[ix][ib];
//               sxqb[ADDR(ix,ib,nbb)]= sxq[ADDR(ix,iq,nq)]+ 2*w*swnb[ADDR(ix,ib,nbb)];
//           }
//        }
//        #pragma acc exit data copyout(this) 
//     }
////exit(0);
//  }
