using namespace std;

#  include <field/gas.h>

   void shear_z( Int ic, Real w, Int iql, Real wl, Real *ql[], Real *auxl[], Int iqr, Real wr, Real *qr[], Real *auxr[],
                 Real *xc[], Real *wc[], Real *wxdc[], Int nv0, Int naux, Real *ztau );

#pragma acc routine seq
   void voffset2(Real f, Real ptch, Real *var)
  {
      Real x,y,z,cth,sth;

      cth= cos(f*ptch);
      sth= sin(f*ptch);

      x= var[0];
      y= var[1];
      z= var[2];
      var[0]= x;
      var[1]= y*cth+ z*sth;
      var[2]=-y*sth+ z*cth;
  }

   cGas::cGas( cCosystem *Coo, cVisc *visc )
  {
      Int i,ia,ja;

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel(); 

      nv=0;
      naux=0;
      nauxf=0;
      nlhs=0;

      nvk=0;

  }


   void cGas::voffset( Int iqs, Int iqe, Real f, Int *isrc, Real *qsrc[], Int *idst, Real *qdst[] )
  {
      Int iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( iv=nvel;iv<nv;iv++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               i0= iq;
               if( isrc ){ i0= isrc[i0]; };
               i1= iq;
               if( idst ){ i1= idst[i1]; };
               qdst[iv][i1]= qsrc[iv][i0];
           }
        }
         coo->voffset( iqs, iqe, f, isrc, qsrc, idst, qdst );
      }
  }

   void cGas::voffset( Int iqs, Int iqe, Real f, Int *isrc, Real *sqsrc, Int *idst, Real *sqdst, Int nqsrc, Int nqdst )
  {
      Int iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( iv=nvel;iv<nv;iv++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               i0= isrc[iq];
               i1= iq;
               sqdst[ADDR(iv,i1,nqdst)]= sqsrc[ADDR(iv,i0,nqsrc)];
           }
        }
         coo->voffset( iqs, iqe, f, isrc, sqsrc, idst, sqdst,nqsrc,nqdst );
      }
  }

   void cGas::voffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *sqsrc, Int *idst, Real *sqdst, Int nqsrc, Int nqdst )
  {
      Int iq,iv,i0,i1;
      if( iqe > iqs )
     {
   //      for( iq=iqs;iq<iqe;iq++ )
   //     {
   //         for( iv=nvel;iv<nv;iv++ )
   //        {
   //            i0= isrc[iq];
   //            i1= iq;
   //            sqdst[ADDR(iv,i1,nqdst)]= sqsrc[ADDR(iv,i0,nqsrc)];
   //        }
   //     }
         coo->voffsetgpu( iqs, iqe, f, isrc, sqsrc, idst, sqdst,nqsrc,nqdst );
      }
  }

   void cGas::voffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& qsrc, cAu3xView<Int>& idst, cAu3xView<Real>& qdst )
  {
      Int iq,iv,i0,i1;
      if( iqe > iqs )
     {
   //      for( iq=iqs;iq<iqe;iq++ )
   //     {
   //         for( iv=nvel;iv<nv;iv++ )
   //        {
   //            i0= isrc[iq];
   //            i1= iq;
   //            sqdst[ADDR(iv,i1,nqdst)]= sqsrc[ADDR(iv,i0,nqsrc)];
   //        }
   //     }
         coo->voffset( iqs, iqe, f, isrc, qsrc, idst, qdst );
      }
  }

   void cGas::voffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *qsrc_re[], Real *qsrc_im[], 
                                                              Int *idst, Real *qdst_re[], Real *qdst_im[] )
  {
      Int iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( iv=nvel;iv<nv;iv++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               i0= iq;
               if( isrc ){ i0= isrc[i0]; };
               i1= iq;
               if( idst ){ i1= idst[i1]; };
               qdst_re[iv][i1]= qsrc_re[iv][i0];
               qdst_im[iv][i1]= qsrc_im[iv][i0];
           }
        }
         coo->voffset_z( iqs, iqe, f, ibpa, isrc, qsrc_re, qsrc_im, idst, qdst_re, qdst_im );
      }
  }

  /* void cGas::goffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *ijdx[], Int *isrc, Real *dxdxsrc[], Real **dqdxsrc_re[], Real **dqdxsrc_im[],
                                                                           Int *idst, Real *dxdxdst[], Real **dqdxdst_re[], Real **dqdxdst_im[] )
  {
      Real           *tmp[3], *tmp_re[3], *tmp_im[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dqdxdst_re[iv][ix][i1]= dqdxsrc_re[iv][ix][i0];
                  dqdxdst_im[iv][ix][i1]= dqdxsrc_im[iv][ix][i0];
              }
           }
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[ix][jx]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[jx][ix]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }

         for( iv=0;iv<nv;iv++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               tmp_re[ix]= dqdxdst_re[iv][ix];
               tmp_im[ix]= dqdxdst_im[iv][ix];
           }
            coo->voffset( iqs, iqe, f, ibpa, idst, tmp_re, idst, tmp_re );
            coo->voffset( iqs, iqe, f, ibpa, idst, tmp_re, idst, tmp_re );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nvel;iv++ )
           {
               tmp_re[iv]= dqdxdst_re[iv][ix];
               tmp_im[iv]= dqdxdst_im[iv][ix];
           }
            coo->voffset( iqs, iqe, f, ibpa, idst, tmp_re, tmp_im, idst, tmp_re, tmp_im );
        }
     }
  }*/

   void cGas::goffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *ijdx[], Int *isrc, Real *dxdxsrc[], Real **dqdxsrc_re[], Real **dqdxsrc_im[],
                                                                           Int *idst, Real *dxdxdst[], Real **dqdxdst_re[], Real **dqdxdst_im[] )
  {
      Real           *tmp[3], *tmp_re[3], *tmp_im[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dqdxdst_re[iv][ix][i1]= dqdxsrc_re[iv][ix][i0];
                  dqdxdst_im[iv][ix][i1]= dqdxsrc_im[iv][ix][i0];
              }
           }
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[ix][jx]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[jx][ix]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }

         for( iv=0;iv<nv;iv++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               tmp_re[ix]= dqdxdst_re[iv][ix];
               tmp_im[ix]= dqdxdst_im[iv][ix];
           }
            coo->voffset( iqs, iqe, f, idst, tmp_re, idst, tmp_re );
            coo->voffset( iqs, iqe, f, idst, tmp_im, idst, tmp_im );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nvel;iv++ )
           {
               tmp_re[iv]= dqdxdst_re[iv][ix];
               tmp_im[iv]= dqdxdst_im[iv][ix];
           }
            coo->voffset( iqs, iqe, f, idst, tmp_re, idst, tmp_re );
            coo->voffset( iqs, iqe, f, idst, tmp_im, idst, tmp_im );
        }
     }
  }

   void cGas::goffset( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *dxdxsrc[], Real **dqdxsrc[],
                                                              Int *idst, Real *dxdxdst[], Real **dqdxdst[] )
  {
      Real           *tmp[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
              }
           }
            for( iv=0;iv<nv;iv++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
              }
           }
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[ix][jx]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( jx=0;jx<nx;jx++ )
           {
               tmp[jx]= dxdxdst[ijdx[jx][ix]];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }

         for( iv=0;iv<nv;iv++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               tmp[ix]= dqdxdst[iv][ix];
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nvel;iv++ )
           {
               tmp[iv]= dqdxdst[iv][ix];
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
     }
  }

   void cGas::goffset( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *sdxdxsrc, Real *sdqdxsrc,
                                                              Int *idst, Real *sdxdxdst, Real *sdqdxdst,
                                                              Int nqsrc, Int nqdst )
  {
      Real           tmp[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            i0= isrc[iq];
            i1= iq;
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxdst[ADDR(ix,jx,i1,nqdst)]= sdxdxsrc[ADDR(ix,jx,i0,nqsrc)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(ix,jx,iq,nqdst)];     
              }
               coo->voffset(  f, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(ix,jx,iq,nqdst)] = tmp[jx];     
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(jx,ix,iq,nqdst)];     
              }
               coo->voffset( f, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(jx,ix,iq,nqdst)] = tmp[jx];     
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               coo->voffset( f, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               coo->voffset( f, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
     }
  }

   void cGas::goffsetgpu( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *sdxdxsrc, Real *sdqdxsrc,
                                                                 Int *idst, Real *sdxdxdst, Real *sdqdxdst,
                                                                 Int nqsrc, Int nqdst )
  {
      Int ix,jx,iq,iv,i0,i1;
      Real           tmp[3],ptch;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;
      if( iqe > iqs )
     {
        //nqsrc: nq, nqdst:nprq
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp)\
         present (isrc[0:nqdst],sdxdxsrc[0:nx*nx*nqsrc],sdqdxsrc[0:nx*nv*nqsrc],sdxdxdst[0:nx*nx*nqdst],sdqdxdst[0:nx*nv*nqdst],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            i0= isrc[iq];
            i1= iq;
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxdst[ADDR(ix,jx,i1,nqdst)]= sdxdxsrc[ADDR(ix,jx,i0,nqsrc)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(ix,jx,iq,nqdst)];     
              }
               //coo->voffset(  f, tmp );
               voffset2(f, ptch, tmp);
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(ix,jx,iq,nqdst)] = tmp[jx];     
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(jx,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(jx,ix,iq,nqdst)] = tmp[jx];     
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
        #pragma acc enter data copyin(this)
     }
  }

   void cGas::goffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc_view, cAu3xView<Real>& dxdxsrc, cAu3xView<Real>& dqdxsrc,
                                                 cAu3xView<Int>& idst_view, cAu3xView<Real>& dxdxdst, cAu3xView<Real>& dqdxdst )
  {
      Int ix,jx,iq,iv,i0,i1;
      Real           tmp[3],ptch;
      cTabData data;
      Int asct;

      Int nqsrc, nqdst;
      Int *isrc; 
      Real *sdxdxsrc, *sdqdxsrc;
      Int *idst; 
      Real *sdxdxdst, *sdqdxdst;

      nqsrc = dqdxsrc.get_dim2();
      nqdst = dqdxdst.get_dim2();
     
      isrc = isrc_view.get_data(); 
      sdxdxsrc = dxdxsrc.get_data(); 
      sdqdxsrc = dqdxsrc.get_data();
      idst = idst_view.get_data(); 
      sdxdxdst = dxdxdst.get_data(); 
      sdqdxdst = dqdxdst.get_data();

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;
      if( iqe > iqs )
     {
        //nqsrc: nq, nqdst:nprq
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp)\
         present (isrc[0:nqdst],sdxdxsrc[0:nx*nx*nqsrc],sdqdxsrc[0:nx*nv*nqsrc],sdxdxdst[0:nx*nx*nqdst],sdqdxdst[0:nx*nv*nqdst],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            i0= isrc[iq];
            i1= iq;
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxdst[ADDR(ix,jx,i1,nqdst)]= sdxdxsrc[ADDR(ix,jx,i0,nqsrc)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(ix,jx,iq,nqdst)];     
              }
               //coo->voffset(  f, tmp );
               voffset2(f, ptch, tmp);
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(ix,jx,iq,nqdst)] = tmp[jx];     
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(jx,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(jx,ix,iq,nqdst)] = tmp[jx];     
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];
              }
               //coo->voffset( f, tmp );
               voffset2(f, ptch, tmp);
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
        #pragma acc enter data copyin(this)
     }
  }
   void cGas::goffset( Int iqs, Int iqe, Real f, Real *sdxdxsrc, Real *sdqdxsrc, Int nqsrc )
  {
      Real           tmp[3], sdxdxdst[3*10],sdqdxdst[3*10],ptch;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      Int ix,jx,iq,iv,i0;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp,sdxdxdst,sdqdxdst)\
         present (sdxdxsrc[0:nx*nx*nqsrc],sdqdxsrc[0:nx*nv*nqsrc],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            i0= iq;
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxdst[ADDR(ix,jx,0,1)]= sdxdxsrc[ADDR(ix,jx,i0,nqsrc)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                  sdqdxdst[ADDR(iv,ix,0,1)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(ix,jx,0,1)];     
              }
               //coo->voffset(  f, tmp );
               voffset2(  f, ptch, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(ix,jx,0,1)] = tmp[jx];     
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(jx,ix,0,1)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(jx,ix,0,1)] = tmp[jx];     
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,0,1)];
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,0,1)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,0,1)];
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,0,1)] = tmp[iv];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxsrc[ADDR(ix,jx,i0,nqsrc)] = sdxdxdst[ADDR(ix,jx,0,1)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                 sdqdxsrc[ADDR(iv,ix,i0,nqsrc)] = sdqdxdst[ADDR(iv,ix,0,1)];
              }
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::goffset( Int iqs, Int iqe, Real f, cAu3xView<Real>& dxdxsrc, cAu3xView<Real>& dqdxsrc )
  {
      Real           tmp[3], sdxdxdst[3*10],sdqdxdst[3*10],ptch;
      cTabData data;
      Int asct;

      Int nqsrc;
      Real *sdxdxsrc, *sdqdxsrc;

      nqsrc = dqdxsrc.get_dim2();
      sdxdxsrc = dxdxsrc.get_data(); 
      sdqdxsrc = dqdxsrc.get_data();

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      Int ix,jx,iq,iv,i0;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp,sdxdxdst,sdqdxdst)\
         present (sdxdxsrc[0:nx*nx*nqsrc],sdqdxsrc[0:nx*nv*nqsrc],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            i0= iq;
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxdst[ADDR(ix,jx,0,1)]= sdxdxsrc[ADDR(ix,jx,i0,nqsrc)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                  sdqdxdst[ADDR(iv,ix,0,1)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(ix,jx,0,1)];     
              }
               //coo->voffset(  f, tmp );
               voffset2(  f, ptch, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(ix,jx,0,1)] = tmp[jx];     
              }
           }

            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  tmp[jx]= sdxdxdst[ADDR(jx,ix,0,1)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( jx=0;jx<3;jx++ )
              {
                  sdxdxdst[ADDR(jx,ix,0,1)] = tmp[jx];     
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,0,1)];
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,0,1)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,0,1)];
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,0,1)] = tmp[iv];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( jx=0;jx<3;jx++ )
              {
                  //dxdxdst[ijdx[ix][jx]][i1]= dxdxsrc[ijdx[ix][jx]][i0];
                  sdxdxsrc[ADDR(ix,jx,i0,nqsrc)] = sdxdxdst[ADDR(ix,jx,0,1)];
              }

               for( iv=0;iv<nv;iv++ )
              {
                  //dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
                 sdqdxsrc[ADDR(iv,ix,i0,nqsrc)] = sdqdxdst[ADDR(iv,ix,0,1)];
              }
           }
        }
        #pragma acc exit data delete(this)
     }
  }
   void cGas::goffset( Int iqs, Int iqe, Real f, Int *isrc, Real **dqdxsrc[], 
                                                 Int *idst, Real **dqdxdst[] )
  {
      Real           *tmp[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  i0= iq;
                  if( isrc ){ i0= isrc[i0]; };
                  i1= iq;
                  if( idst ){ i1= idst[i1]; };
                  dqdxdst[iv][ix][i1]= dqdxsrc[iv][ix][i0];
              }
           }
        }
         for( iv=0;iv<nv;iv++ )
        {
            for( ix=0;ix<nx;ix++ )
           {
               tmp[ix]= dqdxdst[iv][ix];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
         for( ix=0;ix<nx;ix++ )
        {
            for( iv=0;iv<nvel;iv++ )
           {
               tmp[iv]= dqdxdst[iv][ix];     
           }
            coo->voffset( iqs, iqe, f, idst, tmp, idst, tmp );
        }
     }
  }

   void cGas::goffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *sdqdxsrc, 
                                                    Int *idst, Real *sdqdxdst, 
                                                    Int nqsrc, Int nqdst )
  {
      Real           tmp[3],ptch;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp)\
         present (isrc[0:nqdst],sdqdxsrc[0:nx*nv*nqsrc],sdqdxdst[0:nx*nv*nqdst],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  i0= isrc[iq];
                  i1= iq;
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
     }
  }

   void cGas::goffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc_view, cAu3xView<Real>& dqdxsrc, 
                                                 cAu3xView<Int>& idst_view, cAu3xView<Real>& dqdxdst )
  {
      Real           tmp[3],ptch;
      cTabData data;
      Int asct;

      Int nqdst, nqsrc;
      Int *isrc; 
      Real *sdqdxsrc; 
      Int *idst;
      Real *sdqdxdst;

      nqsrc = dqdxsrc.get_dim2();      
      nqdst = dqdxdst.get_dim2();      

      isrc     = isrc_view.get_data();
      sdqdxsrc = dqdxsrc.get_data();
      //idst   = idst_view.get_data();
      sdqdxdst = dqdxdst.get_data();

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(tmp)\
         present (isrc[0:nqdst],sdqdxsrc[0:nx*nv*nqsrc],sdqdxdst[0:nx*nv*nqdst],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  i0= isrc[iq];
                  i1= iq;
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               //coo->voffset( f, tmp );
               voffset2( f, ptch, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
     }
  }
   void cGas::goffset( Int iqs, Int iqe, Real f, Int *isrc, Real *sdqdxsrc, 
                                                 Int *idst, Real *sdqdxdst, 
                                                 Int nqsrc, Int nqdst )
  {
      Real           tmp[3];

      Int ix,jx,iq,iv,i0,i1;
      if( iqe > iqs )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nv;iv++ )
              {
                  i0= isrc[iq];
                  i1= iq;
                  sdqdxdst[ADDR(iv,ix,i1,nqdst)]= sdqdxsrc[ADDR(iv,ix,i0,nqsrc)];
              }
           }

            for( iv=0;iv<nv;iv++ )
           {
               for( ix=0;ix<3;ix++ )
              {
                  tmp[ix]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               coo->voffset( f, tmp );
               for( ix=0;ix<3;ix++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[ix];
              }
           }
            for( ix=0;ix<3;ix++ )
           {
               for( iv=0;iv<nvel;iv++ )
              {
                  tmp[iv]= sdqdxdst[ADDR(iv,ix,iq,nqdst)];     
              }
               coo->voffset( f, tmp );
               for( iv=0;iv<nvel;iv++ )
              {
                  sdqdxdst[ADDR(iv,ix,iq,nqdst)] = tmp[iv];
              }
           }
        }
     }
  }


   void cGas::roffset( Int iqs, Int iqe, Real f, Int *isrc, Real *rhssrc[], Int *idst, Real *rhsdst[] )
  {
      coo->voffset( iqs, iqe, f, isrc, rhssrc+1, idst, rhsdst+1 );
  }

   void cGas::roffset( Int iqs, Int iqe, Real f, Real *srhs, Int nq )
  {
      Int iq;
      Real tmp[3];

      if(iqe>iqs)
     {
         for(iq=iqs;iq<iqe;iq++)
        {
            tmp[0] = srhs[ADDR(1,iq,nq)];        
            tmp[1] = srhs[ADDR(2,iq,nq)];        
            tmp[2] = srhs[ADDR(3,iq,nq)];      
            coo->voffset( f, tmp );
	    srhs[ADDR(1,iq,nq)] = tmp[0];
            srhs[ADDR(2,iq,nq)] = tmp[1];
            srhs[ADDR(3,iq,nq)] = tmp[2];
        }
     }
  }

   void cGas::roffsetgpu( Int iqs, Int iqe, Real f, Real *srhs, Int nq )
  {
      Int iq;
      Real tmp[3],ptch;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      if(iqe>iqs)
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tmp) \
         present(srhs[0:nv*nq],this) \
         default(none)
         for(iq=iqs;iq<iqe;iq++)
        {
            tmp[0] = srhs[ADDR(1,iq,nq)];        
            tmp[1] = srhs[ADDR(2,iq,nq)];        
            tmp[2] = srhs[ADDR(3,iq,nq)];      
            voffset2( f, ptch, tmp );
	    srhs[ADDR(1,iq,nq)] = tmp[0];
            srhs[ADDR(2,iq,nq)] = tmp[1];
            srhs[ADDR(3,iq,nq)] = tmp[2];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::roffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *srhs, Int *idst, Real *sdst, Int nqsrc, Int nqdst )
  {
      Int iq,jq;
      Real tmp[3],ptch;
      cTabData data;
      Int asct;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      if(iqe>iqs)
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tmp) \
         present(isrc[0:nqdst],srhs[0:nv*nqsrc],sdst[0:nv*nqdst],this) \
         default(none)
         for(iq=iqs;iq<iqe;iq++)
        {
            jq = isrc[iq];
            tmp[0] = srhs[ADDR(1,jq,nqsrc)];        
            tmp[1] = srhs[ADDR(2,jq,nqsrc)];        
            tmp[2] = srhs[ADDR(3,jq,nqsrc)];      
            voffset2( f, ptch, tmp );
	    sdst[ADDR(1,iq,nqdst)] = tmp[0];
            sdst[ADDR(2,iq,nqdst)] = tmp[1];
            sdst[ADDR(3,iq,nqdst)] = tmp[2];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::roffset( Int iqs, Int iqe, Real f, cAu3xView<Real>& rhs )
  {
      Int iq;
      Real tmp[3],ptch;
      cTabData data;
      Int asct;

      Int nq;
      Real *srhs;

      nq = rhs.get_dim1();
      srhs = rhs.get_data();

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      if(iqe>iqs)
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tmp) \
         present(srhs[0:nv*nq],this) \
         default(none)
         for(iq=iqs;iq<iqe;iq++)
        {
            tmp[0] = srhs[ADDR(1,iq,nq)];
            tmp[1] = srhs[ADDR(2,iq,nq)];
            tmp[2] = srhs[ADDR(3,iq,nq)];
            voffset2( f, ptch, tmp );
            srhs[ADDR(1,iq,nq)] = tmp[0];
            srhs[ADDR(2,iq,nq)] = tmp[1];
            srhs[ADDR(3,iq,nq)] = tmp[2];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::roffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc_view, cAu3xView<Real>& rhs, cAu3xView<Int>& idst_view, cAu3xView<Real>& dst )
  {
      Int iq,jq;
      Real tmp[3],ptch;
      cTabData data;
      Int asct;

      Int nqsrc, nqdst;
      Int *isrc;
      Real *srhs, *sdst;

      nqsrc = rhs.get_dim1();
      nqdst = dst.get_dim1();

      isrc = isrc_view.get_data();
      srhs = rhs.get_data();
      sdst = dst.get_data();

      coo->get( &data );
      data.get( "assembly-sectors", &asct );
      ptch = pi2/(Real)asct;

      if(iqe>iqs)
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         private(tmp) \
         present(isrc[0:nqdst],srhs[0:nv*nqsrc],sdst[0:nv*nqdst],this) \
         default(none)
         for(iq=iqs;iq<iqe;iq++)
        {
            jq = isrc[iq];
            tmp[0] = srhs[ADDR(1,jq,nqsrc)];
            tmp[1] = srhs[ADDR(2,jq,nqsrc)];
            tmp[2] = srhs[ADDR(3,jq,nqsrc)];
            voffset2( f, ptch, tmp );
            sdst[ADDR(1,iq,nqdst)] = tmp[0];
            sdst[ADDR(2,iq,nqdst)] = tmp[1];
            sdst[ADDR(3,iq,nqdst)] = tmp[2];
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::roffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *rhssrc_re[], Real *rhssrc_im[], 
                                                              Int *idst, Real *rhsdst_re[], Real *rhsdst_im[] )
  {
      coo->voffset_z( iqs, iqe, f, ibpa, isrc, rhssrc_re, rhssrc_im, idst, rhsdst_re, rhsdst_im );
  }



   //void cGas::mwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                   Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,  
                                      Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr, 
                                      Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr ) 
  {
      //vsc->mwflx( ics,ice, icql, xl, ql, auxl, rhsl,  icqr, xr, qr, auxr, rhsr, wc, wxdc, auxc );
      vsc->mwflx( ics,ice, icql, sxl, sql, sauxl, srhsl,  icqr, sxr, sqr, sauxr, srhsr, swc, swxdc, sauxc, nql, nqr );
  }

   void cGas::mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                      cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                      cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      //vsc->mwflx( ics,ice, icql, xl, ql, auxl, rhsl,  icqr, xr, qr, auxr, rhsr, wc, wxdc, auxc );
      vsc->mwflx( ics,ice, icql, xl, ql, auxl, rhsl,  icqr, xr, qr, auxr, rhsr, wc, wxdc, auxc );
  }

   //void cGas::dmwflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                    Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::dmwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                       Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                       Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ) 
  {
      //vsc->dmwflx( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
      //vsc->dmwflx_new( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
      vsc->dmwflx_new( ics,ice, icql, sxl,sql,sauxl,sdql,sdauxl,sresl, icqr, sxr,sqr,sauxr,sdqr,sdauxr,sresr, swc,swxdc,sauxc,nbb,nq );
  }

   void cGas::dmwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                       cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                       cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      //vsc->dmwflx( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
      //vsc->dmwflx_new( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
      vsc->dmwflx_new( ics,ice, icql, xl,ql,auxl,dql,dauxl,resl, icqr, xr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

   void cGas::dmwflx_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],
                                         Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[],
                                         Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      vsc->dmwflx_z( ics,ice, icql, xl,ql,auxl,zl_re,zl_im,rhsl_re, rhsl_im, icqr, xr,qr,auxr,zr_re,zr_im,rhsr_re, rhsr_im, wc,wxdc,auxc );
  }

   void cGas::accel( Int iqs, Int iqe, Real omega, Real *wq[], Real *xq[], Real *q[], Real *aux[], Real *xdq[], Real *rhs[] )
  {
      Int iq,ia;
      coo->accel( iqs,iqe, omega, xq,q,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
        }
     }
      for( ia=0;ia<nx;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]-=  q[nvel+1][iq]*wq[ia+1][iq];
        }
     }
  }

   void cGas::accel( Int iqs, Int iqe, Real omega, Real *swq, Real *sxq, Real *sq, Real *saux, Real *sxdq, Real *srhs, Int nq )
  {
      Real uy,uz;
      Int iq,ia;
      //coo->accel( iqs,iqe, omega, sxq,sq,sxdq,nq ); 
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present (swq[0:(nx+1)*nq],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sxdq[0:nvel*nq],srhs[0:nv*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         uy= sq[ADDR(1,iq,nq)];
         uz= sq[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<nvel;ia++ )
        {
            //rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*saux[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }

         for( ia=0;ia<nx;ia++ )
        {
            //rhs[ia+1][iq]-=  q[nvel+1][iq]*wq[ia+1][iq];
            srhs[ADDR(ia+1,iq,nq)]-=  sq[ADDR(nvel+1,iq,nq)]*swq[ADDR(ia+1,iq,nq)];
        }
     }
      #pragma acc exit data delete(this)
  }

   void cGas::accel( Int iqs, Int iqe, Real omega, cAu3xView<Real>& wq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& xdq, cAu3xView<Real>& rhs )
  {
      Real uy,uz;
      Int iq,ia;

//THIS ACCEL ONLY WORKS FOR ANNULAR COORDINATE SYSTEM, HAVE A LOOK AT THE OLD ONE AND MAKE IT WORK FOR OTHER COORDINATE SYSTEM
      Int nq;
      Real *swq, *sxq, *sq,*saux, *sxdq, *srhs;

      nq = xq.get_dim1();
      swq  = wq.get_data(); 
      sxq  = xq.get_data();
      sq   = q.get_data();
      saux = aux.get_data(); 
      sxdq = xdq.get_data(); 
      srhs = rhs.get_data();;

      //coo->accel( iqs,iqe, omega, sxq,sq,sxdq,nq ); 
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present (swq[0:(nx+1)*nq],sxq[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sxdq[0:nvel*nq],srhs[0:nv*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         uy= sq[ADDR(1,iq,nq)];
         uz= sq[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<nvel;ia++ )
        {
            //rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*saux[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }

         for( ia=0;ia<nx;ia++ )
        {
            //rhs[ia+1][iq]-=  q[nvel+1][iq]*wq[ia+1][iq];
            srhs[ADDR(ia+1,iq,nq)]-=  sq[ADDR(nvel+1,iq,nq)]*swq[ADDR(ia+1,iq,nq)];
        }
     }
      #pragma acc exit data delete(this)
  }

   void cGas::daccel( Int iqs, Int iqe, Real omega, Real *wq[], Real *xq[], Real *q[], Real *dq[], Real *daux[], Real *aux[], 
                      Real *xdq[], Real *rhs[] )
  {
      Int iq,ia;
      coo->daccel( iqs,iqe, omega, xq,q,daux,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]+=  wq[0][iq]*aux[0][iq]*xdq[ia][iq];
        }
     }
      coo->accel( iqs,iqe, omega, xq,q,xdq ); 
      for( ia=0;ia<nvel;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            //rhs[ia+1][iq]+=  wq[0][iq]*daux[0][iq]*xdq[ia][iq];
            rhs[ia+1][iq]+=  wq[0][iq]*dq[0][iq]*xdq[ia][iq];
        }
     }
      for( ia=0;ia<nx;ia++ )
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            rhs[ia+1][iq]-=  daux[nvel+1][iq]*wq[ia+1][iq];
        }
     }
  }

   void cGas::daccel( Int iqs, Int iqe, Real omega, Real *swq, Real *sxq, Real *sq, Real *sdq, Real *sdaux, Real *saux, 
                      Real *sxdq, Real *srhs, Int nq )
  {
      Int iq,ia;
      Real uy,uz;
      //coo->daccel( iqs,iqe, omega, sxq,sq,sdaux,sxdq,nq ); 
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present (swq[0:(nx+1)*nq],sxq[0:nx*nq],sq[0:nv*nq],sdq[0:nv*nq],sdaux[0:nv*nq],saux[0:naux*nq],sxdq[0:nvel*nq],srhs[0:nv*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         uy= sdaux[ADDR(1,iq,nq)];
         uz= sdaux[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<3;ia++ )
        {
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*saux[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }

         uy= sq[ADDR(1,iq,nq)];
         uz= sq[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<3;ia++ )
        {
            //rhs[ia+1][iq]+=  wq[0][iq]*daux[0][iq]*xdq[ia][iq];
            //rhs[ia+1][iq]+=  wq[0][iq]*dq[0][iq]*xdq[ia][iq];
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*sdq[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }
         for( ia=0;ia<3;ia++ )
        {
            //rhs[ia+1][iq]-=  daux[3+1][iq]*wq[ia+1][iq];
            srhs[ADDR(ia+1,iq,nq)]-=  sdaux[ADDR(3+1,iq,nq)]*swq[ADDR(ia+1,iq,nq)];
        }
     }
      #pragma acc exit data delete(this)
  }

   void cGas::daccel( Int iqs, Int iqe, Real omega, cAu3xView<Real>& wq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& aux,
                      cAu3xView<Real>& xdq, cAu3xView<Real>& rhs )
  {
      Int iq,ia;
      Real uy,uz;

      Int nq;
      Real *swq, *sxq, *sq, *sdq, *sdaux, *saux, *sxdq, *srhs;

      nq    = xq.get_dim1(); 
      swq   = wq.get_data(); 
      sxq   = xq.get_data(); 
      sq    = q.get_data(); 
      sdq   = dq.get_data();
      sdaux = daux.get_data();
      saux  = aux.get_data();
      sxdq  = xdq.get_data(); 
      srhs  = rhs.get_data();

      //coo->daccel( iqs,iqe, omega, sxq,sq,sdaux,sxdq,nq ); 
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       present (swq[0:(nx+1)*nq],sxq[0:nx*nq],sq[0:nv*nq],sdq[0:nv*nq],sdaux[0:nv*nq],saux[0:naux*nq],sxdq[0:nvel*nq],srhs[0:nv*nq],this) \
       default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         uy= sdaux[ADDR(1,iq,nq)];
         uz= sdaux[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<3;ia++ )
        {
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*saux[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }

         uy= sq[ADDR(1,iq,nq)];
         uz= sq[ADDR(2,iq,nq)];
         sxdq[ADDR(0,iq,nq)]=        0;
         sxdq[ADDR(1,iq,nq)]=-uz*omega;
         sxdq[ADDR(2,iq,nq)]= uy*omega;

         for( ia=0;ia<3;ia++ )
        {
            //rhs[ia+1][iq]+=  wq[0][iq]*daux[0][iq]*xdq[ia][iq];
            //rhs[ia+1][iq]+=  wq[0][iq]*dq[0][iq]*xdq[ia][iq];
            srhs[ADDR(ia+1,iq,nq)]+=  swq[ADDR(0,iq,nq)]*sdq[ADDR(0,iq,nq)]*sxdq[ADDR(ia,iq,nq)];
        }
         for( ia=0;ia<3;ia++ )
        {
            //rhs[ia+1][iq]-=  daux[3+1][iq]*wq[ia+1][iq];
            srhs[ADDR(ia+1,iq,nq)]-=  sdaux[ADDR(3+1,iq,nq)]*swq[ADDR(ia+1,iq,nq)];
        }
     }
      #pragma acc exit data delete(this)
  }

   void cGas::slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq )
  {
  }

   void cGas::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
  }

   //void cGas::srhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Int *idst[], Int *igdst[], Real *wq[], Real *rhs[], Real *lhsa[] )
   void cGas::srhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Int *sidst, Int *sigdst, Real *swq, Real *srhs, Real *slhsa, Int nq )
  {
      //vsc->srhs( iqs,iqe, cfl, q,aux, dqdx,dst,idst,igdst,wq,rhs, lhsa );
      vsc->srhs( iqs,iqe, cfl, sq,saux, sdqdx,sdst,NULL,NULL,swq,srhs, slhsa, nq );
  }

   void cGas::srhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhsa )
  {
      //vsc->srhs( iqs,iqe, cfl, q,aux, dqdx,dst,idst,igdst,wq,rhs, lhsa );
      vsc->srhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,rhs, lhsa );
  }

   //void cGas::dsrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
   //                     Real *wq[], Real *res[], Real *lhs[] )
   void cGas::dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst, 
                        Real *swq, Real *sres, Real *slhs, Int nq )
  {
      if( iqe > iqs )
     {
         //vsc->dsrhs( iqs,iqe, cfl,q,aux,dq,daux, dqdx,dst,wq,res,lhs );
         vsc->dsrhs( iqs,iqe, cfl,sq,saux,sdq,sdaux,sdqdx,sdst,swq,sres,slhs,nq );
     }

  }

   void cGas::dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                     cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs )
  {
      if( iqe > iqs )
     {
         //vsc->dsrhs( iqs,iqe, cfl,q,aux,dq,daux, dqdx,dst,wq,res,lhs );
         vsc->dsrhs( iqs,iqe, cfl,q,aux,dq,daux,dqdx,dst,wq,res,lhs );
     }

  }

   void cGas::vrhs( Int iqs, Int iqe, Real f, Real *u[], Real *aux[], Real **dqdx[], Real *dst[], 
                        Real *wq[], Real *rhs[], Real *lhs[] )
  {
      Int  iv,iq;
      if( iqe > iqs )
     {
         for( iv=0;iv<nv;iv++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
//             rhs[iv][iq]+= f*lhs[nlhs-1][iq]*u[iv][iq];
               rhs[iv][iq]+= f*wq[0][iq]*u[iv][iq];
           }
        }
     }
  }

   //void cGas::dvrhs( Int iqs, Int iqe, Real cfl, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
   //                     Real *wq[], Real *res[], Real *lhs[] )
   void cGas::dvrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                     cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs )
  {
      Int  iv,iq;

      Int nq;
      Real *sq, *saux, *sdq, *sdaux, *sdqdx, *sdst, *swq, *sres, *slhs;

      nq    = q.get_dim1();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data();
      sdaux = daux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      sres  = res.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               //res[iv][iq]= lhs[nlhs-1][iq]*dq[iv][iq]- res[iv][iq];
               sres[ADDR(iv,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]*sdq[ADDR(iv,iq,nq)]- sres[ADDR(iv,iq,nq)];
           }
        }
        #pragma acc exit data copyout(this)
     }
  }

   void cGas::dvrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst, 
                        Real *swq, Real *sres, Real *slhs, Int nq )
  {
      Int  iv,iq;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               //res[iv][iq]= lhs[nlhs-1][iq]*dq[iv][iq]- res[iv][iq];
               sres[ADDR(iv,iq,nq)]= slhs[ADDR(nlhs-1,iq,nq)]*sdq[ADDR(iv,iq,nq)]- sres[ADDR(iv,iq,nq)];
           }
        }
        #pragma acc exit data copyout(this)
     }
  }

   //void cGas::mflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
   //                                     Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
   //                                               Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::mflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                      Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                      Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           {
               mflx33( ics,ice, icql,sxl,sql,sauxl,sdqdxl,srhsl, icqr,sxr,sqr,sauxr,sdqdxr,srhsr,sxc,swc,swxdc,sauxc,nfc,nq );
           }
            vsc->mflx( ics,ice, icql,sxl,sql,sauxl,sdqdxl,srhsl, icqr,sxr,sqr,sauxr,sdqdxr,srhsr,sxc,swc,swxdc,sauxc,nfc,nq );
        }
     }
  }

   void cGas::mflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                      cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                      cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           {
               mflx33( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr,xc,wc,wxdc,auxc );
           }
            vsc->mflx( ics,ice, icql,xl,ql,auxl,dqdxl,rhsl, icqr,xr,qr,auxr,dqdxr,rhsr,xc,wc,wxdc,auxc );
        }
     }
  }

   //void cGas::mflx( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real **dqdx[], Real *rhs[],
   //                 Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::mflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                    Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           {
               //mflx33( ics,ice, icq,x,q,aux,dqdx,rhs,xc,wc,wxdc,auxc );
               mflx33( ics,ice, sicq,sx,sq,saux,sdqdx,srhs,sxc,swc,swxdc,sauxc, nfc, nq );
           }
            vsc->mflx( ics,ice, sicq,sx,sq,saux,sdqdx,srhs,sxc,swc,swxdc,sauxc, nfc, nq );
        }
     }
  }

   void cGas::mflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           {
               //mflx33( ics,ice, icq,x,q,aux,dqdx,rhs,xc,wc,wxdc,auxc );
               mflx33( ics,ice, icq,x,q,aux,dqdx,rhs,xc,wc,wxdc,auxc );
           }
            vsc->mflx( ics,ice, icq,x,q,aux,dqdx,rhs,xc,wc,wxdc,auxc );
        }
     }
  }

   //void cGas::dmflx( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], 
   //                  Real *resl[], Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], 
   //                  Real *resr[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::dmflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl, 
                                       Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr, 
                                       Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           { 
               dmflx33(ics,ice,icql,sxl,sql,sauxl,sdql,sdauxl,sresl,
                               icqr,sxr,sqr,sauxr,sdqr,sdauxr,sresr,
                               sxc,swc,swxdc,sauxc,nfc,nq);
           }
        }
     }
      vsc->dmflx( ics,ice, icql,sxl,sql,sauxl,sdql,sdauxl,sresl, 
                           icqr,sxr,sqr,sauxr,sdqr,sdauxr,sresr,
                           sxc,swc,swxdc,sauxc,nfc,nq );
  }

   void cGas::dmflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                       cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                       cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           { 
               dmflx33(ics,ice,icql,xl,ql,auxl,dql,dauxl,resl,
                               icqr,xr,qr,auxr,dqr,dauxr,resr,
                               xc,wc,wxdc,auxc);
           }
        }
     }
      vsc->dmflx( ics,ice, icql,xl,ql,auxl,dql,dauxl,resl, 
                           icqr,xr,qr,auxr,dqr,dauxr,resr,
                           xc,wc,wxdc,auxc );
  }

   //void cGas::dmflx( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real *dq[], Real *daux[], 
   //                  Real *res[], Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::dmflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                     cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           { 
               dmflx33(ics,ice,icq,x,q,aux,dq,daux,res,xc,wc,wxdc,auxc);
           }
        }
     }
      vsc->dmflx( ics,ice, icq,x,q,aux,dq,daux,res,xc,wc,wxdc,auxc );
  }

   void cGas::dmflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux, 
                     Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      if( vsc->viscous() )
     {
         if( ice > ics )
        {
            if(vsc->gettype() != sbslearsm_visc)
           { 
               dmflx33(ics,ice,sicq,sx,sq,saux,sdq,sdaux,sres,sxc,swc,swxdc,sauxc,nfc,nq);
           }
        }
     }
      vsc->dmflx( ics,ice, sicq,sx,sq,saux,sdq,sdaux,sres,sxc,swc,swxdc,sauxc,nfc,nq );
  }

  // void cGas::maux( Int iqs, Int iqe, Real *q[], Real *dst[], Real *dqdx[][3], Real *aux[] )
  //{
  //    vsc->maux( iqs,iqe, q,dst,dqdx, aux );
  //}
   void cGas::maux( Int iqs, Int iqe, Real *xq[], Real *q[], Real *dst[], Real **dqdx[], Real *aux[], Real lmixmax )//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] )
  {
      vsc->maux( iqs,iqe,xq,q,dst,dqdx, aux, lmixmax );//, ng, iqbq, auxb );//, igdst, idst );
  }

   void cGas::maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq )
  {
      vsc->maux( iqs,iqe,sxq,sq,sdst,sdqdx,saux,lmixmax,nq );//, ng, iqbq, auxb );//, igdst, idst );
  }

   void cGas::maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux )
  {
      vsc->maux( iqs,iqe,xq,q,dst,dqdx,aux );//, ng, iqbq, auxb );//, igdst, idst );
  }

   void cGas::nondim( Int iqs, Int iqe, Real *q[], Int *idone )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
/*          if( idone[ia] != 1 )
           {
               for( iq=iqs;iq<iqe;iq++ )
              {
                  q[ia][iq]= deflt[0];
              }
           }*/
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]/= unit[0];
           }
        }
         ia= nvel;
/*       if( idone[ia] != 1 )   
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]= deflt[1];
           }
        }*/
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]/= unit[1];
        }

         ia= nvel+1;
/*       if( idone[ia] != 1 )   
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]= deflt[2];
           }
        }*/
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]/= unit[2];
        }
         vsc->nondim( iqs,iqe, q, idone);
     }     
  }

   void cGas::nondim( Int iqs, Int iqe, cAu3xView<Real>& q, Int *idone )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         Int nq;
         Real *sq;
            
         nq = q.get_dim1();
         sq = q.get_data();

        #pragma acc enter data copyin(this)
        #pragma acc enter data copyin(unit[0:nv])

        #pragma acc parallel loop\
         present (sq[0:nv*nq],idone[0:nv],unit[0:nv],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ia=0;ia<nvel;ia++ )
           {
               //q(ia,iq)/= unit[0];
               sq[ADDR(ia,iq,nq)]/= unit[0];
           }

            ia= nvel;
            //q(ia,iq)/= unit[1];
            sq[ADDR(ia,iq,nq)]/= unit[1];

            ia= nvel+1;
            //q(ia,iq)/= unit[2];
            sq[ADDR(ia,iq,nq)]/= unit[2];
        }
        #pragma acc exit data copyout(this)
        #pragma acc exit data copyout(unit[0:nv])

         vsc->nondim( iqs,iqe, q, idone);
     }
  }

   void cGas::nondim_time( Real *dtm )
  {
      *dtm = (*dtm) * unit[0];
  }


   void cGas::redim( Int iqs, Int iqe, Real *q[] )
  {
      Int ia,iq;

      if( iqe > iqs )
     {
         for( ia=0;ia<nvel;ia++ )
        {
            for( iq=iqs;iq<iqe;iq++ )
           {
               q[ia][iq]*= unit[0];
           }
        }
         ia= nvel;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]*= unit[1];
        }
         ia= nvel+1;
         for( iq=iqs;iq<iqe;iq++ )
        {
            q[ia][iq]*= unit[2];
        }
         vsc->redim( iqs,iqe, q );
     }
  }

   void cGas::redim( Int iqs, Int iqe, cAu3xView<Real>& q )
  {
      Int ia,iq;
   
      if( iqe > iqs )
     {
         Int nq;
         Real *sq;
            
         nq = q.get_dim1();
         sq = q.get_data();

        #pragma acc enter data copyin(this)
        #pragma acc enter data copyin(unit[0:nv])
        #pragma acc enter data copyin(sq[0:nv*nq])
        
        #pragma acc parallel loop\
         present (sq[0:nv*nq],unit[0:nv],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ia=0;ia<nvel;ia++ )
           {
               //q(ia,iq)*= unit[0];
               sq[ADDR(ia,iq,nq)]*= unit[0];
           }

            ia = nvel;
            sq[ADDR(ia,iq,nq)]*= unit[1];

            ia = nvel+1;
            sq[ADDR(ia,iq,nq)]*= unit[2];
        }
//         ia= nvel;
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            q(ia,iq)*= unit[1];
//        }
//         ia= nvel+1;
//         for( iq=iqs;iq<iqe;iq++ )
//        { 
//            q(ia,iq)*= unit[2];
//        }
         vsc->redim( iqs,iqe, q );

        #pragma acc exit data copyout(this)
        #pragma acc exit data copyout(unit[0:nv])
     }
  }

   void cGas::rendim_time( Real *dtm )
  {
      *dtm = (*dtm) / unit[0];
  }

   void cGas::redim_cpu( Int iqs, Int iqe, cAu3xView<Real>& q )
  {
      Int ia,iq;
   
      if( iqe > iqs )
     {
         Int nq;
         Real *sq;
            
         nq = q.get_dim1();
         sq = q.get_data();

         for( iq=iqs;iq<iqe;iq++ )
        {
            for( ia=0;ia<nvel;ia++ )
           {
               //q(ia,iq)*= unit[0];
               sq[ADDR(ia,iq,nq)]*= unit[0];
           }

            ia = nvel;
            sq[ADDR(ia,iq,nq)]*= unit[1];

            ia = nvel+1;
            sq[ADDR(ia,iq,nq)]*= unit[2];
        }
//         ia= nvel;
//         for( iq=iqs;iq<iqe;iq++ )
//        {
//            q(ia,iq)*= unit[1];
//        }
//         ia= nvel+1;
//         for( iq=iqs;iq<iqe;iq++ )
//        { 
//            q(ia,iq)*= unit[2];
//        }
         vsc->redim_cpu( iqs,iqe, q );
     }
  }

   void cGas::auxv( Int iqs, Int iqe, Real *q[], Real *aux[] )
  {
      if( iqe > iqs )
     {
         auxv3( iqs,iqe, q,aux );
     }
  }
   void cGas::auxv( Int iqs, Int iqe, Real *q, Real *aux, Int nq )
  {
      if( iqe > iqs )
     {
         auxv3( iqs,iqe, q,aux, nq );
     }
  }
   void cGas::auxvgpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq )
  {
      if( iqe > iqs )
     {
         auxv3gpu( iqs,iqe, q,aux, nq );
     }
  }
   void cGas::auxv( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, string arch )
  {
      if( iqe > iqs )
     {
         auxv3( iqs,iqe, q,aux, arch );
     }
  }
   void cGas::cnsv( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia; 

      if( iqe > iqs )
     {
         cnsv3( iqs,iqe, q,aux,qo );
     } 
  }
   void cGas::cnsv( Int iqs, Int iqe, Real *sq, Real *saux, Real *sqo, Int nq )
  {

      Int iq,ia; 

      if( iqe > iqs )
     {
         cnsv3( iqs,iqe, sq,saux,sqo,nq );
     } 
  }
   void cGas::cnsv( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {

      Int iq,ia;

      if( iqe > iqs )
     {
         cnsv3( iqs,iqe, q,aux,qo, arch );
     }
  }


   void cGas::csv_to_pri( Int iqs, Int iqe, Real *qo[], Real *q[] )
  {

      Int iq,ia; 

      if( iqe > iqs )
     {
         csv_to_pri3( iqs,iqe, qo, q );
     } 
  }

   void cGas::cnsv_z( Int iqs, Int iqe, Real *q[], Real *aux[], Real *qo[] )
  {

      Int iq,ia; 

      if( iqe > iqs )
     {
         cnsv3_z( iqs,iqe, q,aux,qo );
     } 
  }

   //void cGas::wflx( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],  
   //                                  Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
   //                                  Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::wflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,  
                                     Int *icqr, Real *sqr, Real *sauxr, Real *srhsr, 
                                     Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      if( ice > ics )
     {

         //setv( ics,ice, nauxf,0., auxc );
         for( Int j=0;j<nauxf;j++ )
        {
            setv( ics,ice, ZERO, &sauxc[j*nfc] );
        }

         wflx33( ics,ice, icql,sql,sauxl,srhsl,  icqr,sqr,sauxr,srhsr,swc,swxdc,sauxc,nfc,nq );
     }
  }

   void cGas::wflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                     cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                     cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
  
         setv( ics,ice, nauxf,ZERO, auxc, "d" );
        // for( Int j=0;j<nauxf;j++ )
        //{
        //    setv( ics,ice, ZERO, &sauxc[j*nfc] );
        //}
                                       
         wflx33( ics,ice, icql,ql,auxl,rhsl,  icqr,qr,auxr,rhsr,wc,wxdc,auxc );
     }
  } 

   void cGas::iflxmuscl( Int ics,Int ice, Int *icql, Int idl, Real *sxql, Real *sql, Real *sdxdxl, Real *sdqdxl, Real *sauxl, Real *srhsl,
                                          Int *icqr, Int idr, Real *sxqr, Real *sqr, Real *sdxdxr, Real *sdqdxr, Real *sauxr, Real *srhsr, 
                                          Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ) 
  {
      if( ice > ics )
     {
         //setv( ics,ice, nauxf,0., auxc );
         for( Int j=0;j<nauxf;j++ )
        {
            setv( ics,ice, ZERO, &sauxc[j*nfc] );
        }

         iflxmuscl33( ics,ice, icql,idl,sxql,sql,sdxdxl,sdqdxl,sauxl,srhsl,
                               icqr,idr,sxqr,sqr,sdxdxr,sdqdxr,sauxr,srhsr,
                               sxc, swc, swxdc, sauxc, nfc, nq, iorder );

     }
  }

   void cGas::iflxmuscl( Int ics,Int ice, cAu3xView<Int>& icql, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                          cAu3xView<Int>& icqr, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                          cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      if( ice > ics )
     {
         setv( ics,ice, nauxf,ZERO, auxc, "d" );
        // for( Int j=0;j<nauxf;j++ )
        //{
        //    setv( ics,ice, ZERO, &sauxc[j*nfc] );
        //}


         iflxmuscl33( ics,ice, icql,idl,xql,ql,dxdxl,dqdxl,auxl,rhsl,
                               icqr,idr,xqr,qr,dxdxr,dqdxr,auxr,rhsr,
                               xc, wc, wxdc, auxc,iorder );

     }
  }

   void cGas::iflxmuscl( Int ics,Int ice, Int *sicq, Real *sxq, Real *sq, Real *sdxdx, Real *sdqdx, Real *saux, Real *srhs,
                         Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ) 
  {
      if( ice > ics )
     {
         for( Int j=0;j<nauxf;j++ )
        {
            setv( ics,ice, ZERO, &sauxc[j*nfc] );
        }
         //setv( ics,ice, nauxf,0., auxc ); //need to replace this with sauxc

         iflxmuscl33( ics,ice, sicq,sxq,sq,sdxdx,sdqdx,saux,srhs,
                      sxc, swc, swxdc, sauxc, nfc, nq, iorder );

     }
  }

   void cGas::iflxmuscl( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                         cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      if( ice > ics )
     {
        // for( Int j=0;j<nauxf;j++ )
        //{
        //    setv( ics,ice, ZERO, &sauxc[j*nfc] );
        //}
         //setv( ics,ice, nauxf,0., auxc ); //need to replace this with sauxc
         setv( ics,ice, nauxf,ZERO, auxc, "d" );

         iflxmuscl33( ics,ice, icq,xq,q,dxdx,dqdx,aux,rhs,
                      xc, wc, wxdc, auxc, iorder );

     }
  }

   //void cGas::dwflx( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::dwflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                      Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                      Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ) 
  {
      if( ice > ics )
     {
         //dwflx33(  ics,ice,  icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
         dwflx33(  ics,ice,  icql,sql,sauxl,sdql,sdauxl,sresl, icqr,sqr,sauxr,sdqr,sdauxr,sresr, swc,swxdc,sauxc,nbb,nq );
     }
  }

   void cGas::dwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                      cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                      cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         //dwflx33(  ics,ice,  icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
         dwflx33(  ics,ice,  icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
     }
  }

   //void cGas::diflx( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::diflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                      Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                      Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      if( ice > ics )
     {
         diflx33( ics,ice, icql,sql,sauxl,sdql,sdauxl,sresl, icqr,sqr,sauxr,sdqr,sdauxr,sresr, swc,swxdc,sauxc,nfc,nq );
     }
  }

   void cGas::diflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                      cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                      cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         diflx33( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
     }
  }

   //void cGas::diflx( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::diflx( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                     cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      if( ice > ics )
     {
         diflx33( ics,ice, icq,q,aux,dq,daux,res,wc,wxdc,auxc );
     }
  }

   void cGas::diflx( Int ics,Int ice, Int *sicq, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                     Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
  {
      if( ice > ics )
     {
         diflx33( ics,ice, sicq,sq,saux,sdq,sdaux,sres,swc,swxdc,sauxc,nfc,nq );
     }
  }

   void cGas::diflxmuscl( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                           Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                           Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd )
  {
      if( ice > ics )
     {
         diflxmuscl33( ics, ice, idl, icql, xql, ql0, zl0, dxdxl, dqdxl, dzdxl, resl,
                       idr, icqr, xqr, qr0, zr0, dxdxr, dqdxr, dzdxr, resr,
                       xc, wc, wxdc, auxc, grd );
     }
  }

   //void cGas::diflxb( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                   Int *icqr, Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[],
   //                                   Real *wc[], Real *wxdc[], Real *auxc[] ) 
   void cGas::diflxb( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                       Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                       Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ) 
  {

      if( ice > ics )
     {
         //diflxb33( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
         diflxb33( ics,ice, icql,sql,sauxl,sdql,sdauxl,sresl, icqr,sqr,sauxr,sdqr,sdauxr,sresr, swc,swxdc,sauxc,nbb,nq );
     }
  }

   void cGas::diflxb( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                       cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                       cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {

      if( ice > ics )
     {
         //diflxb33( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
         diflxb33( ics,ice, icql,ql,auxl,dql,dauxl,resl, icqr,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
     }
  }

   void cGas::iflx( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
                                     Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                                Real *wc[], Real *wxdc[], Real *auxc[] ) 
 {
      if( ice > ics )
     {
         iflx33( ics,ice, icql,ql,auxl,rhsl, icqr,qr,auxr,rhsr, wc,wxdc,auxc );
     }
 
 }

   void cGas::iflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,
                                     Int *icqr, Real *sqr, Real *sauxr, Real *srhsr, 
                                     Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) 
 {
      if( ice > ics )
     {
         iflx33( ics,ice, icql,sql,sauxl,srhsl, icqr,sqr,sauxr,srhsr, swc,swxdc,sauxc,nfc,nq );
     }
 
 }

   void cGas::iflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                     cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                     cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
 {
      if( ice > ics )
     {
         iflx33( ics,ice, icql,ql,auxl,rhsl, icqr,qr,auxr,rhsr, wc,wxdc,auxc );
     }
  
 }

   void cGas::iflx( Int ics, Int ice, Int *icq[2], Real *q[], Real *aux[], Real *rhs[],
                    Real *wc[], Real *wxdc[], Real *auxc[] ) 
 {
      if( ice > ics )
     {
         iflx33( ics,ice,icq,q,aux,rhs,wc,wxdc,auxc );
     }
 
 }
 
   void cGas::dvar( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dU[], Real *dq[] )
  {
      if( iqe > iqs )
     {
         dvar3( iqs,iqe, q,aux, dU,dq );
         vsc->dvar(iqs,iqe,q,aux,dU,dq );
     }
  }

   void cGas::dvar( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      if( iqe > iqs )
     {
         dvar3( iqs,iqe, q,aux, dU,dq,nq );
         vsc->dvar(iqs,iqe,q,aux,dU,dq,nq );
     }
  }

   void cGas::dvargpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq )
  {
      if( iqe > iqs )
     {
         dvar3gpu( iqs,iqe, q,aux, dU,dq,nq );
         vsc->dvargpu(iqs,iqe,q,aux,dU,dq,nq );
     }
  }

   void cGas::dvar( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq )
  {
      if( iqe > iqs )
     {
         dvar3( iqs,iqe, q,aux, dU,dq );
         vsc->dvar(iqs,iqe,q,aux,dU,dq );
     }
  }

   //void cGas::mflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
   //                                     Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
   //                                     Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::mflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                        Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                        Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];
// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] = sdqdxl[ADDR(iv,0,iql,nql)];
               dqdxl[iv][1] = sdqdxl[ADDR(iv,1,iql,nql)];
               dqdxl[iv][2] = sdqdxl[ADDR(iv,2,iql,nql)];

               dqdxr[iv][0] = sdqdxr[ADDR(iv,0,iqr,nqr)];
               dqdxr[iv][1] = sdqdxr[ADDR(iv,1,iqr,nqr)];
               dqdxr[iv][2] = sdqdxr[ADDR(iv,2,iqr,nqr)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               //q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;
               q[iv]= wl*sql[ADDR(iv,iql,nql)]+ wr*sqr[ADDR(iv,iqr,nqr)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[     0][iql]+ wr*auxr[     0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                        cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                        cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;
      Int             nql, nqr;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdqdxl, *srhsl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqdxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdqdxl = dqdxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqdxr = dqdxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr) \
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxl,dqdxr)\
         present(sxl[0:nx*nfc],sql[0:nv*nfc],sauxl[0:naux*nfc],sdqdxl[0:nv*nx*nfc],srhsl[0:nv*nfc],\
                 icqr[0:nfc],sxr[0:nx*nq],sqr[0:nv*nq],sauxr[0:naux*nq],sdqdxr[0:nv*nx*nq],srhsr[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] = sdqdxl[ADDR(iv,0,iql,nql)];
               dqdxl[iv][1] = sdqdxl[ADDR(iv,1,iql,nql)];
               dqdxl[iv][2] = sdqdxl[ADDR(iv,2,iql,nql)];

               dqdxr[iv][0] = sdqdxr[ADDR(iv,0,iqr,nqr)];
               dqdxr[iv][1] = sdqdxr[ADDR(iv,1,iqr,nqr)];
               dqdxr[iv][2] = sdqdxr[ADDR(iv,2,iqr,nqr)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdxl[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdxl[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdxl[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdxr[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdxr[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdxr[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( qr[iv][iqr]-ql[iv][iql] )/w;
               //q[iv]= wl*ql[iv][iql]+ wr*qr[iv][iqr];
               dqn[iv]= ( sqr[ADDR(iv,iqr,nqr)]-sql[ADDR(iv,iql,nql)] )/w;
               q[iv]= wl*sql[ADDR(iv,iql,nql)]+ wr*sqr[ADDR(iv,iqr,nqr)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdxl[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdxl[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdxr[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[     0][iql]+ wr*auxr[     0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(     0,iql,nql)]+ wr*sauxr[ADDR(     0,iqr,nqr)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhsr[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhsl[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhsr[ADDR_(iv,iqr,nqr)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhsl[ADDR_(iv,iql,nql)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cGas::mflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q0[], Real *aux[], Real **dqdx0[], Real *rhs[],
   //                   Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::mflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                      Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxr,dqdxl)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {

            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] =  sdqdx[ADDR(iv,0,iql,nq)];
               dqdxl[iv][1] =  sdqdx[ADDR(iv,1,iql,nq)];
               dqdxl[iv][2] =  sdqdx[ADDR(iv,2,iql,nq)];

               dqdxr[iv][0] =  sdqdx[ADDR(iv,0,iqr,nq)];
               dqdxr[iv][1] =  sdqdx[ADDR(iv,1,iqr,nq)];
               dqdxr[iv][2] =  sdqdx[ADDR(iv,2,iqr,nq)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdx0[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdx0[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdx0[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdx0[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdx0[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdx0[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( q0[iv][iqr]-q0[iv][iql] )/w;
               //q[iv]= wl*q0[iv][iql]+ wr*q0[iv][iqr];
               dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;
               q[iv]= wl*sq[ADDR(iv,iql,nq)]+ wr*sq[ADDR(iv,iqr,nq)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdx0[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[     0][iql]+ wr*aux[     0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhs[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhs[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhs[ADDR_(iv,iqr,nq)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhs[ADDR_(iv,iql,nq)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
            
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                      cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) 
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dqnl[MxNVs],dqtl,dl;
      Real            dqnr[MxNVs],dqtr,dr;
      Real            dqn[MxNVs],dqt[MxNVs][3],dqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            q[MxNVs];
      Real            rho,mu,kappa,div;
      Real            dqdxl[MxNVs][3];
      Real            dqdxr[MxNVs][3];

      Int nq, nfc;    
      
      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      Int *sicq;
      Real *sx, *sq, *saux, *sdqdx, *srhs, *sxc, *swc, *swxdc, *sauxc;

      sicq = icq.get_data();
      sx = x.get_data();
      sq = q0.get_data();
      saux = aux.get_data();
      sdqdx = dqdx0.get_data();
      srhs = rhs.get_data();
      sxc = xc.get_data();
      swc = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

// temporary location: move to viscous behaviour class

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(dqnl,dqnr,dqn,dqt,dqdx,f,tau,taun,q,wn,xn,dqdxr,dqdxl)\
         present(sicq[0:nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nv*nx*nq], srhs[0:nv*nq],\
                 sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)

         for( ic=ics;ic<ice;ic++ )
        {

            //iql= icq[0][ic]; 
            //iqr= icq[1][ic]; 
            iql= sicq[ADDR(0,ic,nfc)]; 
            iqr= sicq[ADDR(1,ic,nfc)]; 

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

// normal gradients
            for( iv=0;iv<nv0;iv++ )
           {
               dqdxl[iv][0] =  sdqdx[ADDR(iv,0,iql,nq)];
               dqdxl[iv][1] =  sdqdx[ADDR(iv,1,iql,nq)];
               dqdxl[iv][2] =  sdqdx[ADDR(iv,2,iql,nq)];

               dqdxr[iv][0] =  sdqdx[ADDR(iv,0,iqr,nq)];
               dqdxr[iv][1] =  sdqdx[ADDR(iv,1,iqr,nq)];
               dqdxr[iv][2] =  sdqdx[ADDR(iv,2,iqr,nq)];
           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqnl[iv]=  dqdx0[iv][0][iql]*wc[0][ic];
               //dqnl[iv]+= dqdx0[iv][1][iql]*wc[1][ic];
               //dqnl[iv]+= dqdx0[iv][2][iql]*wc[2][ic];
               dqnl[iv]=  dqdxl[iv][0]*wn[0];
               dqnl[iv]+= dqdxl[iv][1]*wn[1];
               dqnl[iv]+= dqdxl[iv][2]*wn[2];

               //dqnr[iv]=  dqdx0[iv][0][iqr]*wc[0][ic];
               //dqnr[iv]+= dqdx0[iv][1][iqr]*wc[1][ic];
               //dqnr[iv]+= dqdx0[iv][2][iqr]*wc[2][ic];
               dqnr[iv]=  dqdxr[iv][0]*wn[0];
               dqnr[iv]+= dqdxr[iv][1]*wn[1];
               dqnr[iv]+= dqdxr[iv][2]*wn[2];

               //dqn[iv]= ( q0[iv][iqr]-q0[iv][iql] )/w;
               //q[iv]= wl*q0[iv][iql]+ wr*q0[iv][iqr];
               dqn[iv]= ( sq[ADDR(iv,iqr,nq)]-sq[ADDR(iv,iql,nq)] )/w;
               q[iv]= wl*sq[ADDR(iv,iql,nq)]+ wr*sq[ADDR(iv,iqr,nq)];
           }

// tangential gradients

            for( iv=0;iv<nv0;iv++ )
           {
               //dqtl= dqdx0[iv][0][iql]- wc[0][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][0][iqr]- wc[0][ic]*dqnr[iv];
               dqtl= dqdxl[iv][0]- wn[0]*dqnl[iv];
               dqtr= dqdxr[iv][0]- wn[0]*dqnr[iv];
               dqt[iv][0]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][1][iql]- wc[1][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][1][iqr]- wc[1][ic]*dqnr[iv];
               dqtl= dqdxl[iv][1]- wn[1]*dqnl[iv];
               dqtr= dqdxr[iv][1]- wn[1]*dqnr[iv];
               dqt[iv][1]= wl*dqtl+ wr*dqtr;

               //dqtl= dqdx0[iv][2][iql]- wc[2][ic]*dqnl[iv];
               //dqtr= dqdx0[iv][2][iqr]- wc[2][ic]*dqnr[iv];
               dqtl= dqdxl[iv][2]- wn[2]*dqnl[iv];
               dqtr= dqdxr[iv][2]- wn[2]*dqnr[iv];
               dqt[iv][2]= wl*dqtl+ wr*dqtr;
           }

// reconstruct gradient
            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic]+ dqt[iv][0];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic]+ dqt[iv][1];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic]+ dqt[iv][2];
               dqdx[iv][0]= dqn[iv]*wn[0]+ dqt[iv][0];
               dqdx[iv][1]= dqn[iv]*wn[1]+ dqt[iv][1];
               dqdx[iv][2]= dqn[iv]*wn[2]+ dqt[iv][2];
           }

// stress tensor
            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[     0][iql]+ wr*aux[     0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(     0,iql,nq)]+ wr*saux[ADDR(     0,iqr,nq)];
            div=  dqdx[0][0];
            div+= dqdx[1][1];
            div+= dqdx[2][2];
            div*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 
            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 
            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            tau[0][0]+= div;
            tau[1][1]+= div;
            tau[2][2]+= div;

// viscous fluxes

            //taun[1]=  tau[0][0]*wc[0][ic];
            //taun[1]+= tau[0][1]*wc[1][ic];
            //taun[1]+= tau[0][2]*wc[2][ic];
            taun[1]=  tau[0][0]*wn[0];
            taun[1]+= tau[0][1]*wn[1];
            taun[1]+= tau[0][2]*wn[2];

            //taun[2]=  tau[1][0]*wc[0][ic];
            //taun[2]+= tau[1][1]*wc[1][ic];
            //taun[2]+= tau[1][2]*wc[2][ic];
            taun[2]=  tau[1][0]*wn[0];
            taun[2]+= tau[1][1]*wn[1];
            taun[2]+= tau[1][2]*wn[2];

            //taun[3]=  tau[2][0]*wc[0][ic];
            //taun[3]+= tau[2][1]*wc[1][ic];
            //taun[3]+= tau[2][2]*wc[2][ic];
            taun[3]=  tau[2][0]*wn[0];
            taun[3]+= tau[2][1]*wn[1];
            taun[3]+= tau[2][2]*wn[2];


            taun[4]= -kappa*dqn[3];
            taun[4]+= taun[1]*q[0];
            taun[4]+= taun[2]*q[1];
            taun[4]+= taun[3]*q[2];

// accumulate
            for( iv=1;iv<nv0;iv++ )
           {
               //rhs[iv][iqr]+= taun[iv]*wc[3][ic];
               //rhs[iv][iql]-= taun[iv]*wc[3][ic];
               #pragma acc atomic
               srhs[ADDR_(iv,iqr,nq)]+= taun[iv]*wn[3];
               #pragma acc atomic
               srhs[ADDR_(iv,iql,nq)]-= taun[iv]*wn[3];
           }
            //auxc[nauxf-1][ic]+= wc[3][ic]*mu/(rho*w);
            sauxc[ADDR(nauxf-1,ic,nfc)]+= wn[3]*mu/(rho*w);
           
        }
        #pragma acc exit data delete(this)
     }
  }

   //void cGas::dmflx33( Int ics, Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *dql[], Real *dauxl[], Real *resl[],
   //                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *dqr[], Real *dauxr[], Real *resr[], 
   //                                      Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] )
   void cGas::dmflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                         Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr, 
                                         Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;
      Int             nql, nqr;

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               //dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;
               ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- sdauxl[ADDR(iv,iql,nql)] )/w;
               dqn[iv]=  (    sqr[ADDR(iv,iqr,nqr)]-    sql[ADDR(iv,iql,nql)] )/w;

               //q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               //dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
               q[iv]=  wl*   sql[ADDR(iv,iql,nql)]+ wr*   sqr[ADDR(iv,iqr,nqr)];
               dq[iv]= wl*sdauxl[ADDR(iv,iql,nql)]+ wr*sdauxr[ADDR(iv,iqr,nqr)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               //ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               //ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               //ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(0,iql,nql)]+      wr*sauxr[ADDR(0,iqr,nqr)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                         cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                         cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;
      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sxl, *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sxr, *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sxl    = xl.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sxr    = xr.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      if( ice > ics )
     { 
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         firstprivate(nql,nqr)\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sxl[0:nx*nql],sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
                  icqr[0:nfc],sxr[0:nx*nqr],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
                  sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            //iqr= icqr[ic];
            iqr= icqr[ADDR(0,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );
            wr=  wn[0]*( xn[0]- sxl[ADDR(0,iql,nql)] );
            wr+= wn[1]*( xn[1]- sxl[ADDR(1,iql,nql)] );
            wr+= wn[2]*( xn[2]- sxl[ADDR(2,iql,nql)] );

            //wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sxr[ADDR(0,iqr,nqr)]- xn[0] );
            wl+= wn[1]*( sxr[ADDR(1,iqr,nqr)]- xn[1] );
            wl+= wn[2]*( sxr[ADDR(2,iqr,nqr)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( dauxr[iv][iqr]- dauxl[iv][iql] )/w;
               //dqn[iv]=  ( qr[iv][iqr]-       ql[iv][iql] )/w;
               ddqn[iv]= ( sdauxr[ADDR(iv,iqr,nqr)]- sdauxl[ADDR(iv,iql,nql)] )/w;
               dqn[iv]=  (    sqr[ADDR(iv,iqr,nqr)]-    sql[ADDR(iv,iql,nql)] )/w;

               //q[iv]=  wl*   ql[iv][iql]+ wr*   qr[iv][iqr];
               //dq[iv]= wl*dauxl[iv][iql]+ wr*dauxr[iv][iqr];
               q[iv]=  wl*   sql[ADDR(iv,iql,nql)]+ wr*   sqr[ADDR(iv,iqr,nqr)];
               dq[iv]= wl*sdauxl[ADDR(iv,iql,nql)]+ wr*sdauxr[ADDR(iv,iqr,nqr)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               //ddqdx[iv][0]= ddqn[iv]*wc[0][ic];
               //ddqdx[iv][1]= ddqn[iv]*wc[1][ic];
               //ddqdx[iv][2]= ddqn[iv]*wc[2][ic];
               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*auxl[naux-2][iql]+ wr*auxr[naux-2][iqr];
            //kappa= wl*auxl[naux-1][iql]+ wr*auxr[naux-1][iqr];
            //rho=   wl*auxl[0][iql]+      wr*auxr[0][iqr];
            mu=    wl*sauxl[ADDR(naux-2,iql,nql)]+ wr*sauxr[ADDR(naux-2,iqr,nqr)];
            kappa= wl*sauxl[ADDR(naux-1,iql,nql)]+ wr*sauxr[ADDR(naux-1,iqr,nqr)];
            rho=   wl*sauxl[ADDR(0,iql,nql)]+      wr*sauxr[ADDR(0,iqr,nqr)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //resr[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //resl[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sresr[ADDR_(iv,iqr,nqr)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sresl[ADDR_(iv,iql,nql)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::dmflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                       Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( daux[iv][iqr]- daux[iv][iql] )/w;
               //dqn[iv]=  ( q0[iv][iqr]-       q0[iv][iql] )/w;
               ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;
               dqn[iv]=  (    sq[ADDR(iv,iqr,nq)]-    sq[ADDR(iv,iql,nq)] )/w;

               //q[iv]=  wl*  q0[iv][iql]+ wr*  q0[iv][iqr];
               //dq[iv]= wl*daux[iv][iql]+ wr*daux[iv][iqr];
               q[iv]=  wl*   sq[ADDR(iv,iql,nq)]+ wr*   sq[ADDR(iv,iqr,nq)];
               dq[iv]= wl*sdaux[ADDR(iv,iql,nq)]+ wr*sdaux[ADDR(iv,iqr,nq)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[0][iql]+      wr*aux[0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //res[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //res[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sres[ADDR_(iv,iqr,nq)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sres[ADDR_(iv,iql,nq)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                       cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ic,iv,ix,iql,iqr;
      Real            wl,wr,w;
      Real            dl;
      Real            dr;
      Real            q[MxNVs],dq[MxNVs];
      Real            ddqn[MxNVs],dqn[MxNVs],dqdx[MxNVs][3],ddqdx[MxNVs][3];
      Real            f[MxNVs],wn[4],xn[3];
      Real            tau[3][3],taun[MxNVs];
      Real            dtau[3][3],dtaun[MxNVs];
      Real            rho,mu,kappa,div,ddiv;

      Int nfc, nq;
      Int *sicq;      
      Real *sx, *sq, *saux, *sdq, *sdaux, *sres, *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q0.get_dim1();

      sicq  = icq.get_data();
      sx    = x.get_data();
      sq    = q0.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         private(q,dq,ddqn,dqn,dqdx,ddqdx,f,tau,taun,dtau,dtaun,wn,xn)\
         present (sicq[0:2*nfc],sx[0:nx*nq],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
                  sres[0:nv*nq],sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
         default(none)
 
         for( ic=ics;ic<ice;ic++ )
        {
            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            wn[0]= swc[ADDR(0,ic,nfc)];
            wn[1]= swc[ADDR(1,ic,nfc)];
            wn[2]= swc[ADDR(2,ic,nfc)];
            wn[3]= swc[ADDR(3,ic,nfc)];
   
            xn[0]= sxc[ADDR(0,ic,nfc)];
            xn[1]= sxc[ADDR(1,ic,nfc)];
            xn[2]= sxc[ADDR(2,ic,nfc)];
// distance of DOF positions from face centre

            //wr=  wc[0][ic]*( xc[0][ic]- x[0][iql] );
            //wr+= wc[1][ic]*( xc[1][ic]- x[1][iql] );
            //wr+= wc[2][ic]*( xc[2][ic]- x[2][iql] );
            wr=  wn[0]*( xn[0]- sx[ADDR(0,iql,nq)] );
            wr+= wn[1]*( xn[1]- sx[ADDR(1,iql,nq)] );
            wr+= wn[2]*( xn[2]- sx[ADDR(2,iql,nq)] );

            //wl=  wc[0][ic]*( x[0][iqr]- xc[0][ic] );
            //wl+= wc[1][ic]*( x[1][iqr]- xc[1][ic] );
            //wl+= wc[2][ic]*( x[2][iqr]- xc[2][ic] );
            wl=  wn[0]*( sx[ADDR(0,iqr,nq)]- xn[0] );
            wl+= wn[1]*( sx[ADDR(1,iqr,nq)]- xn[1] );
            wl+= wn[2]*( sx[ADDR(2,iqr,nq)]- xn[2] );

            w= wl+wr;
            wl/= w; 
            wr/= w; 

            for( iv=0;iv<nv0;iv++ )
           {
               //ddqn[iv]= ( daux[iv][iqr]- daux[iv][iql] )/w;
               //dqn[iv]=  ( q0[iv][iqr]-       q0[iv][iql] )/w;
               ddqn[iv]= ( sdaux[ADDR(iv,iqr,nq)]- sdaux[ADDR(iv,iql,nq)] )/w;
               dqn[iv]=  (    sq[ADDR(iv,iqr,nq)]-    sq[ADDR(iv,iql,nq)] )/w;

               //q[iv]=  wl*  q0[iv][iql]+ wr*  q0[iv][iqr];
               //dq[iv]= wl*daux[iv][iql]+ wr*daux[iv][iqr];
               q[iv]=  wl*   sq[ADDR(iv,iql,nq)]+ wr*   sq[ADDR(iv,iqr,nq)];
               dq[iv]= wl*sdaux[ADDR(iv,iql,nq)]+ wr*sdaux[ADDR(iv,iqr,nq)];

           }

            for( iv=0;iv<nv0;iv++ )
           {
               //dqdx[iv][0]= dqn[iv]*wc[0][ic];
               //dqdx[iv][1]= dqn[iv]*wc[1][ic];
               //dqdx[iv][2]= dqn[iv]*wc[2][ic];
               dqdx[iv][0]= dqn[iv]*wn[0];
               dqdx[iv][1]= dqn[iv]*wn[1];
               dqdx[iv][2]= dqn[iv]*wn[2];

               ddqdx[iv][0]= ddqn[iv]*wn[0];
               ddqdx[iv][1]= ddqn[iv]*wn[1];
               ddqdx[iv][2]= ddqn[iv]*wn[2];
           }

// stress tensor

            //mu=    wl*aux[naux-2][iql]+ wr*aux[naux-2][iqr];
            //kappa= wl*aux[naux-1][iql]+ wr*aux[naux-1][iqr];
            //rho=   wl*aux[0][iql]+      wr*aux[0][iqr];
            mu=    wl*saux[ADDR(naux-2,iql,nq)]+ wr*saux[ADDR(naux-2,iqr,nq)];
            kappa= wl*saux[ADDR(naux-1,iql,nq)]+ wr*saux[ADDR(naux-1,iqr,nq)];
            rho=   wl*saux[ADDR(0,iql,nq)]+      wr*saux[ADDR(0,iqr,nq)];

            div =  dqdx[0][0];
            div+=  dqdx[1][1];
            div+=  dqdx[2][2];
            div*= 2./3.*mu;

            ddiv =  ddqdx[0][0];
            ddiv+=  ddqdx[1][1];
            ddiv+=  ddqdx[2][2];
            ddiv*= 2./3.*mu;

            tau[0][0]= -mu*( dqdx[0][0]+ dqdx[0][0] ); 
            tau[1][0]= -mu*( dqdx[0][1]+ dqdx[1][0] ); 
            tau[2][0]= -mu*( dqdx[0][2]+ dqdx[2][0] ); 

            tau[0][1]=  tau[1][0];
            tau[1][1]= -mu*( dqdx[1][1]+ dqdx[1][1] ); 
            tau[2][1]= -mu*( dqdx[1][2]+ dqdx[2][1] ); 

            tau[0][2]=  tau[2][0];
            tau[1][2]=  tau[2][1];
            tau[2][2]= -mu*( dqdx[2][2]+ dqdx[2][2] ); 

            dtau[0][0]= -mu*( ddqdx[0][0]+ ddqdx[0][0] ); 
            dtau[1][0]= -mu*( ddqdx[0][1]+ ddqdx[1][0] ); 
            dtau[2][0]= -mu*( ddqdx[0][2]+ ddqdx[2][0] ); 

            dtau[0][1]=  dtau[1][0];
            dtau[1][1]= -mu*( ddqdx[1][1]+ ddqdx[1][1] ); 
            dtau[2][1]= -mu*( ddqdx[1][2]+ ddqdx[2][1] ); 

            dtau[0][2]=  dtau[2][0];
            dtau[1][2]=  dtau[2][1];
            dtau[2][2]= -mu*( ddqdx[2][2]+ ddqdx[2][2] ); 

            tau[0][0]+=  div;
            tau[1][1]+=  div;
            tau[2][2]+=  div;

            dtau[0][0]+= ddiv;
            dtau[1][1]+= ddiv;
            dtau[2][2]+= ddiv;

// viscous flux

            //taun[1]=    tau[0][0]*wc[0][ic];
            //taun[1]+=   tau[0][1]*wc[1][ic];
            //taun[1]+=   tau[0][2]*wc[2][ic];
            taun[1]=    tau[0][0]*wn[0];
            taun[1]+=   tau[0][1]*wn[1];
            taun[1]+=   tau[0][2]*wn[2];

            //taun[2]=    tau[1][0]*wc[0][ic];
            //taun[2]+=   tau[1][1]*wc[1][ic];
            //taun[2]+=   tau[1][2]*wc[2][ic];
            taun[2]=    tau[1][0]*wn[0];
            taun[2]+=   tau[1][1]*wn[1];
            taun[2]+=   tau[1][2]*wn[2];

            //taun[3]=    tau[2][0]*wc[0][ic];
            //taun[3]+=   tau[2][1]*wc[1][ic];
            //taun[3]+=   tau[2][2]*wc[2][ic];
            taun[3]=    tau[2][0]*wn[0];
            taun[3]+=   tau[2][1]*wn[1];
            taun[3]+=   tau[2][2]*wn[2];

            //dtaun[1]=   dtau[0][0]*wc[0][ic];
            //dtaun[1]+=  dtau[0][1]*wc[1][ic];
            //dtaun[1]+=  dtau[0][2]*wc[2][ic];
            dtaun[1]=   dtau[0][0]*wn[0];
            dtaun[1]+=  dtau[0][1]*wn[1];
            dtaun[1]+=  dtau[0][2]*wn[2];

            //dtaun[2]=   dtau[1][0]*wc[0][ic];
            //dtaun[2]+=  dtau[1][1]*wc[1][ic];
            //dtaun[2]+=  dtau[1][2]*wc[2][ic];
            dtaun[2]=   dtau[1][0]*wn[0];
            dtaun[2]+=  dtau[1][1]*wn[1];
            dtaun[2]+=  dtau[1][2]*wn[2];

            //dtaun[3]=   dtau[2][0]*wc[0][ic];
            //dtaun[3]+=  dtau[2][1]*wc[1][ic];
            //dtaun[3]+=  dtau[2][2]*wc[2][ic];
            dtaun[3]=   dtau[2][0]*wn[0];
            dtaun[3]+=  dtau[2][1]*wn[1];
            dtaun[3]+=  dtau[2][2]*wn[2];

            dtaun[4]= -kappa*ddqn[3];
            dtaun[4]+= taun[1]*dq[0]+ dtaun[1]*q[0];
            dtaun[4]+= taun[2]*dq[1]+ dtaun[2]*q[1];
            dtaun[4]+= taun[3]*dq[2]+ dtaun[3]*q[2];
           
// accumulate

            for( iv=1;iv<nv0;iv++ )
           {
               //res[iv][iqr]+= dtaun[iv]*wc[3][ic];
               //res[iv][iql]-= dtaun[iv]*wc[3][ic];
               #pragma acc atomic
               sres[ADDR_(iv,iqr,nq)]+= dtaun[iv]*wn[3];
               #pragma acc atomic
               sres[ADDR_(iv,iql,nq)]-= dtaun[iv]*wn[3];
           }
        }
        #pragma acc exit data delete(this)
     }
  }

   void cGas::extrap( Int ics,Int ice, Int *icql, Real *ql[], Real *auxl[],
                                       Int *icqr, Real *qr[], Real *auxr[],
                                                  Real *q0[], Real *aux0[],
                                                  Real *wc[], Real *wxdc[], Real *auxc[] )
  {
      if( ice > ics )
     {
         extrap33( ics,ice, icql,ql,auxl, icqr,qr,auxr, q0,aux0, wc,wxdc,auxc );
     }
  }

   //void cGas::qupd( Int iqs, Int iqe, Real *q[], Real *aux[], Real *dq[], Real *daux[] )
   void cGas::qupd( Int iqs, Int iqe, Real *sq, Real *saux, Real *sdq, Real *sdaux, Int nq )
  {
      Int iv,iq;
      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present (sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               sq[ADDR(iv,iq,nq)]+= sdaux[ADDR(iv,iq,nq)];
           } 
        }
        #pragma acc exit data copyout(this)
     }
  }

   void cGas::qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux )
  {
      Int iv,iq;

      Int nq;
      Real *sq, *saux, *sdq, *sdaux;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq.get_data();
      sdaux = daux.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present (sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv;iv++ )
           {
               sq[ADDR(iv,iq,nq)]+= sdaux[ADDR(iv,iq,nq)];
           } 
        }
        #pragma acc exit data copyout(this)
     }
  }
   //void cGas::yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[],
   //                            Real *dst[] )
   void cGas::yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb,
                               Real *sdst, Int nbb, Int nq, Int ndst )
  {
      //vsc->yplus( ist,ien, iqd,xq,q,aux, iqb,qb,auxb, dst );
      vsc->yplus( ist,ien, siqd,sxq,sq,saux, siqb,sqb,sauxb, sdst,nbb,nq,ndst );

  }

   void cGas::yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb, 
                               cAu3xView<Real>& dst )
  {
      //vsc->yplus( ist,ien, iqd,xq,q,aux, iqb,qb,auxb, dst );
      vsc->yplus( ist,ien, iqd,xq,q,aux, iqb,qb,auxb, dst );

  }

   void cGas::offset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *src_re[], Real *src_im[], 
                                                             Int *idst, Real *dst_re[], Real *dst_im[] )
  {
      Int iv, iq, i0, i1;
      complex<Real> z, img(0,1), dz; 
      Real t;

      for(iq=iqs; iq<iqe; iq++)
     {
         i0= iq;
         if( isrc ){ i0= isrc[iq]; };
         i1= iq;
         if( idst ){ i1= idst[iq]; };

         for(iv=0; iv<nv; iv++)
        {
            z = src_re[iv][i0] + src_im[iv][i0]*img;    
            t = f*ibpa*pi2;
            dz = cos(t) + sin(t)*img;
            z = z*dz;
            dst_re[iv][i1] = z.real();
            dst_im[iv][i1] = z.imag();
        } 
     }     
  }

   void cGas::goffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real **dqdxsrc_re[], Real **dqdxsrc_im[], 
                                                              Int *idst, Real **dqdxdst_re[], Real **dqdxdst_im[] )
  {
      Int iv, iq, i0, i1, ix;
      complex<Real> z, img(0,1), dz; 
      Real t;

      for(iq=iqs; iq<iqe; iq++)
     {
         i0= iq;
         if( isrc ){ i0= isrc[iq]; };
         i1= iq;
         if( idst ){ i1= idst[iq]; };

         for(iv=0; iv<nv; iv++)
        {
            for(ix=0; ix<nx; ix++)
           {
               z = dqdxsrc_re[iv][ix][i0] + dqdxsrc_im[iv][ix][i0]*img;    
               t = f*ibpa*pi2;
               dz = cos(t) + sin(t)*img;
               z = z*dz;
               dqdxdst_re[iv][ix][i1] = z.real();
               dqdxdst_im[iv][ix][i1] = z.imag();
           }
        }
     }
  }

   void cGas::dsflx( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zcl_re[], Real *zcl_im[], Real *zl_re[], Real *zl_im[], Real *rhsl[],
                                      Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zcr_re[], Real *zcr_im[], Real *zr_re[], Real *zr_im[], Real *rhsr[],
                                      Real *xc[], Real *wc[], Real *wxdc[], bool bflag )
  {
/*      Int iv, ic, iql, iqr, ix, jx;
      Real fl[10], fr[10], f[10], wl, wr, w, z_re[10], z_im[10];
      //complex<Real> ul[3], rul[3], ur[3], rur[3], img(0,1), z;
      Real gam = 1.4;
      Real runl_re, runl_im, hl_re, hl_im, unl_re, unl_im;
      Real runr_re, runr_im, hr_re, hr_im, unr_re, unr_im;
      Real rg = 287./10000.;
      Real tau[3][3], ztau_im[10], ztau_re[10], ztauu, taun[4];

      if( ice > ics )
     {
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            if( icql ){ iql= icql[ic]; }
            iqr= ic;
            if( icqr ){ iqr= icqr[ic]; }

            if(nvel==2)
           {
               wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
               wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );

               wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
               wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );

               w= wl+wr;
               wl/= w;
               wr/= w;

               unl_re = zl_re[0][iql]*wc[0][ic];
               unl_re+= zl_re[1][iql]*wc[1][ic];
               unl_im = zl_im[0][iql]*wc[0][ic];
               unl_im+= zl_im[1][iql]*wc[1][ic];

               hl_re =  zl_re[2][iql]*rg*gam/(gam-1) + ql[0][iql]*zl_re[0][iql] + ql[1][iql]*zl_re[1][iql];
               hl_im =  zl_im[2][iql]*rg*gam/(gam-1) + ql[0][iql]*zl_im[0][iql] + ql[1][iql]*zl_im[1][iql];

               fl[0]=0;
               fl[1]=auxl[0][iql]*2*(zl_re[0][iql]*unl_re + zl_im[0][iql]*unl_im);
               fl[2]=auxl[0][iql]*2*(zl_re[1][iql]*unl_re + zl_im[1][iql]*unl_im);
               fl[3]=auxl[0][iql]*2*(        hl_re*unl_re +         hl_im*unl_im); 
               for( iv=4;iv<nv;iv++ ){ fl[iv]= 0; }

               runr_re = zcr_re[1][iqr]*wc[0][ic];
               runr_re+= zcr_re[2][iqr]*wc[1][ic];
               runr_im = zcr_im[1][iqr]*wc[0][ic];
               runr_im+= zcr_im[2][iqr]*wc[1][ic];

               unr_re = zr_re[0][iqr]*wc[0][ic];
               unr_re+= zr_re[1][iqr]*wc[1][ic];
               unr_im = zr_im[0][iqr]*wc[0][ic];
               unr_im+= zr_im[1][iqr]*wc[1][ic];

               hr_re =  zr_re[2][iqr]*rg*gam/(gam-1) + qr[0][iqr]*zr_re[0][iqr] + qr[1][iqr]*zr_re[1][iqr];
               hr_im =  zr_im[2][iqr]*rg*gam/(gam-1) + qr[0][iqr]*zr_im[0][iqr] + qr[1][iqr]*zr_im[1][iqr];

               fr[0]=0;
               fr[1]=auxr[0][iqr]*2*(zr_re[0][iqr]*unr_re + zr_im[0][iqr]*unr_im);
               fr[2]=auxr[0][iqr]*2*(zr_re[1][iqr]*unr_re + zr_im[1][iqr]*unr_im);
               fr[3]=auxr[0][iqr]*2*(        hr_re*unr_re +         hr_im*unr_im); 
               for( iv=4;iv<nv;iv++ ){ fr[iv]= 0; }

               for(iv=0; iv<nv0; iv++)
              {
                  f[iv] = fl[iv]*wl + fr[iv]*wr;
                  rhsr[iv][iqr] += f[iv]*wc[2][ic];
                  rhsl[iv][iql] -= f[iv]*wc[2][ic];
              }
           }
            else if(nvel==3)
           {
               wr=  wc[0][ic]*( xc[0][ic]- xl[0][iql] );
               wr+= wc[1][ic]*( xc[1][ic]- xl[1][iql] );
               wr+= wc[2][ic]*( xc[2][ic]- xl[2][iql] );

               wl=  wc[0][ic]*( xr[0][iqr]- xc[0][ic] );
               wl+= wc[1][ic]*( xr[1][iqr]- xc[1][ic] );
               wl+= wc[2][ic]*( xr[2][iqr]- xc[2][ic] );

               w= wl+wr;
               wl/= w;
               wr/= w;

               for(iv=0; iv<nv; iv++)
              {
                  z_re[iv] = zl_re[iv][iql]*wl + zr_re[iv][iqr]*wr;
                  z_im[iv] = zl_im[iv][iql]*wl + zr_im[iv][iqr]*wr;
              }

               ztauu = 0;
//               shear_z( ic, w, iql, wl, zl_re, auxl, iqr, wr, zr_re, auxr, xc, wc, wxdc, nv0, naux, ztau_re );
//               shear_z( ic, w, iql, wl, zl_im, auxl, iqr, wr, zr_im, auxr, xc, wc, wxdc, nv0, naux, ztau_im );
//               ztauu = 2*(ztau_re[0]*z_re[0] + ztau_im[0]*z_im[0]);
//               ztauu+= 2*(ztau_re[1]*z_re[1] + ztau_im[1]*z_im[1]);
//               ztauu+= 2*(ztau_re[2]*z_re[2] + ztau_im[2]*z_im[2]);

               unl_re = zl_re[0][iql]*wc[0][ic];
               unl_re+= zl_re[1][iql]*wc[1][ic];
               unl_re+= zl_re[2][iql]*wc[2][ic];
               unl_im = zl_im[0][iql]*wc[0][ic];
               unl_im+= zl_im[1][iql]*wc[1][ic];
               unl_im+= zl_im[2][iql]*wc[2][ic];

               hl_re =  zl_re[3][iql]*rg*gam/(gam-1) + ql[0][iql]*zl_re[0][iql] + ql[1][iql]*zl_re[1][iql] + ql[2][iql]*zl_re[2][iql];
               hl_im =  zl_im[3][iql]*rg*gam/(gam-1) + ql[0][iql]*zl_im[0][iql] + ql[1][iql]*zl_im[1][iql] + ql[2][iql]*zl_im[2][iql];

               for(ix=0; ix<nx; ix++)
              {
                  for(jx=0; jx<nx; jx++)
                 {
                     tau[ix][jx] = auxl[0][iql]*2*(zl_re[ix][iql]*zl_re[jx][iql] + zl_im[ix][iql]*zl_im[jx][iql]);
                 }
              }
               taun[1] = tau[0][0]*wc[0][ic];
               taun[1]+= tau[0][1]*wc[1][ic];
               taun[1]+= tau[0][2]*wc[2][ic];
               taun[2]=  tau[1][0]*wc[0][ic];
               taun[2]+= tau[1][1]*wc[1][ic];
               taun[2]+= tau[1][2]*wc[2][ic];
               taun[3] = tau[2][0]*wc[0][ic];
               taun[3]+= tau[2][1]*wc[1][ic];
               taun[3]+= tau[2][2]*wc[2][ic];
               taun[4] = taun[1]*ql[0][iql];
               taun[4]+= taun[2]*ql[1][iql];
               taun[4]+= taun[3]*ql[2][iql];

               if(icql || bflag)
              {
               fl[0]=0;
               fl[1]=auxl[0][iql]*2*(zl_re[0][iql]*unl_re + zl_im[0][iql]*unl_im);
               fl[2]=auxl[0][iql]*2*(zl_re[1][iql]*unl_re + zl_im[1][iql]*unl_im);
               fl[3]=auxl[0][iql]*2*(zl_re[2][iql]*unl_re + zl_im[2][iql]*unl_im);
               fl[4]=auxl[0][iql]*2*(        hl_re*unl_re +         hl_im*unl_im); 
               for( iv=5;iv<nv;iv++ ){ fl[iv]= 0; }
//               fl[4]+=taun[4];
//               fl[4]-=ztauu;
              }
               else
              {
                 fl[0] = 0;
                 fl[1] = 0;
                 fl[2] = 0;
                 fl[3] = 0;
                 fl[4] = 0;
              }

               unr_re = zr_re[0][iqr]*wc[0][ic];
               unr_re+= zr_re[1][iqr]*wc[1][ic];
               unr_re+= zr_re[2][iqr]*wc[2][ic];
               unr_im = zr_im[0][iqr]*wc[0][ic];
               unr_im+= zr_im[1][iqr]*wc[1][ic];
               unr_im+= zr_im[2][iqr]*wc[2][ic];

               hr_re =  zr_re[3][iqr]*rg*gam/(gam-1) + qr[0][iqr]*zr_re[0][iqr] + qr[1][iqr]*zr_re[1][iqr] + qr[2][iqr]*zr_re[2][iqr];
               hr_im =  zr_im[3][iqr]*rg*gam/(gam-1) + qr[0][iqr]*zr_im[0][iqr] + qr[1][iqr]*zr_im[1][iqr] + qr[2][iqr]*zr_im[2][iqr];


               for(ix=0; ix<nx; ix++)
              {
                  for(jx=0; jx<nx; jx++)
                 {
                     tau[ix][jx] = auxr[0][iqr]*2*(zr_re[ix][iqr]*zr_re[jx][iqr] + zr_im[ix][iqr]*zr_im[jx][iqr]);
                 }
              }
               taun[1] = tau[0][0]*wc[0][ic];
               taun[1]+= tau[0][1]*wc[1][ic];
               taun[1]+= tau[0][2]*wc[2][ic];
               taun[2] = tau[1][0]*wc[0][ic];
               taun[2]+= tau[1][1]*wc[1][ic];
               taun[2]+= tau[1][2]*wc[2][ic];
               taun[3] = tau[2][0]*wc[0][ic];
               taun[3]+= tau[2][1]*wc[1][ic];
               taun[3]+= tau[2][2]*wc[2][ic];
               taun[4] = taun[1]*qr[0][iqr];
               taun[4]+= taun[2]*qr[1][iqr];
               taun[4]+= taun[3]*qr[2][iqr];

               if(icqr || bflag)
              {
               fr[0]=0;
               fr[1]=auxr[0][iqr]*2*(zr_re[0][iqr]*unr_re + zr_im[0][iqr]*unr_im);
               fr[2]=auxr[0][iqr]*2*(zr_re[1][iqr]*unr_re + zr_im[1][iqr]*unr_im);
               fr[3]=auxr[0][iqr]*2*(zr_re[2][iqr]*unr_re + zr_im[2][iqr]*unr_im);
               fr[4]=auxr[0][iqr]*2*(        hr_re*unr_re +         hr_im*unr_im); 
               for( iv=5;iv<nv;iv++ ){ fr[iv]= 0; }
//               fr[4]+=taun[4];
//               fr[4]-=ztauu;
              }
               else
              {
                 fr[0] = 0;
                 fr[1] = 0;
                 fr[2] = 0;
                 fr[3] = 0;
                 fr[4] = 0;
              }

               for(iv=0; iv<nv0; iv++)
              {
                  f[iv] = fl[iv]*wl + fr[iv]*wr;
                  rhsr[iv][iqr] += f[iv]*wc[3][ic];
                  rhsl[iv][iql] -= f[iv]*wc[3][ic];
              }
           }
        }
     }*/
  }
