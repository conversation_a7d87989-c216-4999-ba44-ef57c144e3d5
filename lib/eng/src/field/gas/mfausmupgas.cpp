using namespace std;

# include <field/gas.h>

   cMfAusmUpGas::cMfAusmUpGas( cCosystem *Coo, cVisc *visc )
  {

      coo= Coo;
      vsc= visc;
      nx=  coo->getnx();
      nvel=coo->getnvel();

      nvk=3;
      nv=2+nvel;
      naux=7;
      nauxf=7;
      nlhs= 2;

      nv0= nv;
      naux0= naux;
      nauxf0= nauxf;
      nlhs0= nlhs;

      vsc->setvrs( nx,nvel, &nv,&naux,&nauxf,&nlhs );
      ilv[0]=nvel;
      ilv[1]=ilv[0]+1;
      ilv[2]=ilv[1]+1;
      unit[0]= 100.;
      unit[1]=   1.;
      unit[2]= unit[0]*unit[0];
      deflt[0]=   0.;
      deflt[1]= 298.;
      deflt[2]= 100000./unit[2];
      rg= 287/unit[2];
      gam=1.4;
      coo->setnv(nv);


      alpha0= 3./16.;
      beta0= 1./8.;
      minf= 0.1;
      sigma0=1;
      delt0=1;
      kp= 0.15;
      ku= 2.00;
  }

   void cMfAusmUpGas::iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            al,unl,rl,pl,hl,fl[MxNVs],als,ml,mlp,plp, tmpql[MxNVs], tmpauxl[100];
      Real            ar,unr,rr,pr,hr,fr[MxNVs],ars,mr,mrm,prm, tmpqr[MxNVs], tmpauxr[100];
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            ma,pa,ra,m0,fa;
      Real            f[MxNVs];
      Real            psi[MxNVs], wn[4], alpha, ugrid, tmpauxc;

      Int             ia,ic,iql,iqr;

      Int nql, nqr;
      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      srhsr = rhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       firstprivate(nql,nqr) \
       private(fl,fr,tmpql,tmpqr, tmpauxl, tmpauxr, f, psi, wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {

         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         for(ia=0; ia< nv; ia++)
        {
            tmpql[ia] = sql[ADDR(ia,iql,nql)];
            tmpqr[ia] = sqr[ADDR(ia,iqr,nqr)];
        }
         tmpauxl[0] = sauxl[ADDR(0,iql,nql)];
         tmpauxl[1] = sauxl[ADDR(1,iql,nql)];
         tmpauxl[2] = sauxl[ADDR(2,iql,nql)];
         tmpauxl[3] = sauxl[ADDR(3,iql,nql)];

         tmpauxr[0] = sauxr[ADDR(0,iqr,nqr)];
         tmpauxr[1] = sauxr[ADDR(1,iqr,nqr)];
         tmpauxr[2] = sauxr[ADDR(2,iqr,nqr)];
         tmpauxr[3] = sauxr[ADDR(3,iqr,nqr)];

         ugrid = swxdc[ADDR(0,ic,nfc)];
         ausm_plus_up_flux( tmpql, tmpauxl, tmpqr, tmpauxr, wn,ugrid, f, &tmpauxc );

//         rhsl[0][iql]-= f[0];
//         rhsl[1][iql]-= f[1];
//         rhsl[2][iql]-= f[2];
//         rhsl[3][iql]-= f[3];
//         rhsl[4][iql]-= f[4];
//
//         rhsr[0][iqr]+= f[0];
//         rhsr[1][iqr]+= f[1];
//         rhsr[2][iqr]+= f[2];
//         rhsr[3][iqr]+= f[3];
//         rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         //rhsr[0][iqr]+= f[0];
         //rhsr[1][iqr]+= f[1];
         //rhsr[2][iqr]+= f[2];
         //rhsr[3][iqr]+= f[3];
         //rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];


         for( ia=5;ia<nv;ia++ )
        {
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]= tmpauxc;
     }
  }

   void cMfAusmUpGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder )
  {
      Real            al,unl,rl,pl,hl,ql[MxNVs],dql[MxNVs],dql0[MxNVs],auxl[MxNVs],als,ml,mlp,plp;
      Real            ar,unr,rr,pr,hr,qr[MxNVs],dqr[MxNVs],dqr0[MxNVs],auxr[MxNVs],ars,mr,mrm,prm;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            f[MxNVs];
      Real            wn[4],xn[3];
      Real            psi[MxNVs];
      Real            ma,pa,ra,m0,fa,alpha, tmpauxc, ugrid;

      Int             ia,ic,iql,iqr;

      Int nql, nqr;
      Int nfc, nq;
      Int *icql;
      Real *sxql, *sql, *sdxdxl, *sdqdxl, *sauxl, *srhsl;
      Int *icqr;
      Real *sxqr, *sqr, *sdxdxr, *sdqdxr, *sauxr, *srhsr;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = qr0.get_dim1();

      icql   = icql_view.get_data();
      sxql   = xql.get_data();
      sql    = ql0.get_data();
      sdxdxl = dxdxl.get_data();
      sdqdxl = dqdxl.get_data();
      sauxl  = auxl0.get_data();
      srhsl  = rhsl.get_data();
      icqr   = icqr_view.get_data();
      sxqr   = xqr.get_data();
      sqr    = qr0.get_data();
      sdxdxr = dxdxr.get_data();
      sdqdxr = dqdxr.get_data();
      sauxr  = auxr0.get_data();
      srhsr  = rhsr.get_data();
      sxc    = xc.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       private(dql0,dqr0,\
               dql,dqr,\
               ql,qr,\
               auxl,auxr,\
               xn,wn,\
               f, psi ) \ 
       present(            sxql[0:nx*nfc],sql[0:nv*nfc],sdxdxl[0:nx*nx*nfc],sdqdxl[0:nv*nx*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sxqr[0:nx*nq] ,sqr[0:nv*nq], sdxdxr[0:nx*nx*nq], sdqdxr[0:nv*nx*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {

         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

//         iql= ic;
//         if( icql ){ iql= icql[ic]; }
//         iqr= ic;
//         if( icqr ){ iqr= icqr[ic]; }
//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
//
//         for( ia=0;ia<nv;ia++ )
//        {
//            ql[ia]= ql0[ia][iql]+ dql[ia];
//            qr[ia]= qr0[ia][iqr]+ dqr[ia];
//        }
         iql= ic;
         iqr= icqr[ADDR(0,ic,nfc)];
//         deltq( iql,idl,sxql,sql,sdxdxl,sdqdxl, iqr,idr,sxqr,sqr,sdxdxr,sdqdxr, xn,wn, dql0,dqr0, dql,dqr,nfc,nq );
         deltq( nv, iql, sxql, sql, sdxdxl, sdqdxl,
                    iqr, sxqr, sqr, sdxdxr, sdqdxr,
                    xn, wn,  dql0, dqr0, dql, dqr, nfc, nq );

         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= ql0[ia][iql]+ dql[ia];
            //qr[ia]= qr0[ia][iqr]+ dqr[ia];
            ql[ia]= sql[ADDR(ia,iql,nfc)]+ (iorder-1)*dql[ia];
            qr[ia]= sqr[ADDR(ia,iqr,nq)] + (iorder-1)*dqr[ia];
        }

// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

         ugrid = swxdc[ADDR(0,ic,nfc)];
         ausm_plus_up_flux( ql, auxl, qr, auxr, wn,ugrid, f, &tmpauxc );

//         rhsl[0][iql]-= f[0];
//         rhsl[1][iql]-= f[1];
//         rhsl[2][iql]-= f[2];
//         rhsl[3][iql]-= f[3];
//         rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(0,iql,nql)]-= f[0];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

//         rhsr[0][iqr]+= f[0];
//         rhsr[1][iqr]+= f[1];
//         rhsr[2][iqr]+= f[2];
//         rhsr[3][iqr]+= f[3];
//         rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhsr[ADDR_(0,iqr,nqr)]+= f[0];
         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            #pragma acc atomic
            srhsl[ADDR_(ia,iql,nql)]-= f[ia];
            #pragma acc atomic
            srhsr[ADDR_(ia,iqr,nqr)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]= tmpauxc;
     }
  }

   void cMfAusmUpGas::iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder ) 
  {
      Real            al,unl,rl,pl,hl,ql[MxNVs],dql[MxNVs],dql0[MxNVs],auxl[MxNVs],als,ml,mlp,plp;
      Real            ar,unr,rr,pr,hr,qr[MxNVs],dqr[MxNVs],dqr0[MxNVs],auxr[MxNVs],ars,mr,mrm,prm;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            f[MxNVs];
      Real            wn[4],xn[3];
      Real            psi[MxNVs];
      Real            ma,pa,ra,m0,fa,alpha, tmpauxc, ugrid;

      Int             ia,ic,iql,iqr;

      Int nfc, nq;
      Int *sicq;
      Real *sxq, *sq, *sdxdx, *sdqdx, *saux, *srhs;
      Real *sxc, *swc, *swxdc, *sauxc;

      nfc = xc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sxq   = xq.get_data();
      sq    = q.get_data();
      sdxdx = dxdx.get_data();
      sdqdx = dqdx.get_data();
      saux  = aux.get_data();
      srhs  = rhs.get_data();
      sxc   = xc.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      #pragma acc enter data copyin(this)
      #pragma acc parallel loop gang vector\
       private(dql0,dqr0,\
               dql,dqr,\
               auxl,auxr,\
               ix,iql,iqr,ia,\
               xn,wn,\
               al,unl,rl,ll1,ll3,ll4,pl,hl,fl,ql,dhl,dal, \
               ar,unr,rr,lr1,lr3,lr4,pr,hr,fr,qr,dhr,dar, \
               le1,le3,le4, \
               aa,a2a,ra,ha,ka,qa,ana,una,unaa,raa, la1,la4,la3,lmax,fa, \
               dw1,dw3,dw4,dw2,dw2a,dr,dq,dun,dp,dpa, \
               dur,dunr,dpr,drr,dtr,tr,dt, \
               dwl,dwr, \
               mr,ml,wl,wr,\
               f, dw5 ) \ 
       present(sq[0:nv*nq],sauxc[0:nauxf*nfc], saux[0:naux*nq], \
                       sxq[0:nx*nq],srhs[0:nv*nq],sdqdx[0:nv*nx*nq],sicq[0:2*nfc], \
                       sxc[0:nx*nfc],swc[0:(nx+1)*nfc],swxdc[0:nfc],sdxdx[0:nx*nx*nq],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         //wn[0]= wc[0][ic];
         //wn[1]= wc[1][ic];
         //wn[2]= wc[2][ic];
         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

         //xn[0]= xc[0][ic];
         //xn[1]= xc[1][ic];
         //xn[2]= xc[2][ic];
         xn[0]= sxc[ADDR(0,ic,nfc)];
         xn[1]= sxc[ADDR(1,ic,nfc)];
         xn[2]= sxc[ADDR(2,ic,nfc)];

         iql = sicq[ADDR(0,ic,nfc)];
         iqr = sicq[ADDR(1,ic,nfc)];

//         iql= ic;
//         if( icql ){ iql= icql[ic]; }
//         iqr= ic;
//         if( icqr ){ iqr= icqr[ic]; }
//         grd->deltq( iql,idl,xql,ql0,dxdxl,dqdxl, iqr,idr,xqr,qr0,dxdxr,dqdxr, xn,wn, dql0,dqr0, dql,dqr );
//
//         for( ia=0;ia<nv;ia++ )
//        {
//            ql[ia]= ql0[ia][iql]+ dql[ia];
//            qr[ia]= qr0[ia][iqr]+ dqr[ia];
//        }
//         iql= ic;
//         iqr= icqr[ADDR(0,ic,nfc)];
//         deltq( iql,idl,sxql,sql,sdxdxl,sdqdxl, iqr,idr,sxqr,sqr,sdxdxr,sdqdxr, xn,wn, dql0,dqr0, dql,dqr,nfc,nq );
         deltq( nv,iql,iqr,sxq,sq,sdxdx,sdqdx,xn,wn,dql0,dqr0,dql,dqr,nq );

         for( ia=0;ia<nv;ia++ )
        {
            //ql[ia]= q0[ia][iql]+ dql[ia];
            //qr[ia]= q0[ia][iqr]+ dqr[ia];
            ql[ia]= sq[ADDR(ia,iql,nq)]+ (iorder-1)*dql[ia];
            qr[ia]= sq[ADDR(ia,iqr,nq)]+ (iorder-1)*dqr[ia];
        }


// left state - auxiliary variables
         auxl[0]=  ql[4]/( rg*ql[3] );
         auxl[1]=  ql[0]*ql[0];
         auxl[1]+= ql[1]*ql[1];
         auxl[1]+= ql[2]*ql[2];
         auxl[1]*= 0.5;
         auxl[2]= gam*rg* ql[3]; 
         auxl[3]= auxl[2]/(gam-1)+ auxl[1];
         auxl[2]= sqrt( auxl[2] );

// right state - auxiliary variables
         auxr[0]= qr[4]/( rg*qr[3] );
         auxr[1]=  qr[0]*qr[0];
         auxr[1]+= qr[1]*qr[1];
         auxr[1]+= qr[2]*qr[2];
         auxr[1]*= 0.5;
         auxr[2]= gam*rg* qr[3]; 
         auxr[3]= auxr[2]/(gam-1)+ auxr[1];
         auxr[2]= sqrt( auxr[2] );

         ugrid = swxdc[ADDR(0,ic,nfc)];
         ausm_plus_up_flux( ql, auxl, qr, auxr, wn,ugrid, f, &tmpauxc );

//         rhsl[0][iql]-= f[0];
//         rhsl[1][iql]-= f[1];
//         rhsl[2][iql]-= f[2];
//         rhsl[3][iql]-= f[3];
//         rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhs[ADDR_(0,iql,nq)]-= f[0];
         #pragma acc atomic
         srhs[ADDR_(1,iql,nq)]-= f[1];
         #pragma acc atomic
         srhs[ADDR_(2,iql,nq)]-= f[2];
         #pragma acc atomic
         srhs[ADDR_(3,iql,nq)]-= f[3];
         #pragma acc atomic
         srhs[ADDR_(4,iql,nq)]-= f[4];

//         rhsr[0][iqr]+= f[0];
//         rhsr[1][iqr]+= f[1];
//         rhsr[2][iqr]+= f[2];
//         rhsr[3][iqr]+= f[3];
//         rhsr[4][iqr]+= f[4];
         #pragma acc atomic
         srhs[ADDR_(0,iqr,nq)]+= f[0];
         #pragma acc atomic
         srhs[ADDR_(1,iqr,nq)]+= f[1];
         #pragma acc atomic
         srhs[ADDR_(2,iqr,nq)]+= f[2];
         #pragma acc atomic
         srhs[ADDR_(3,iqr,nq)]+= f[3];
         #pragma acc atomic
         srhs[ADDR_(4,iqr,nq)]+= f[4];

         for( ia=5;ia<nv;ia++ )
        {
            //rhsl[ia][iql]-= f[ia];
            //rhsr[ia][iqr]+= f[ia];
            #pragma acc atomic
            srhs[ADDR_(ia,iql,nq)]-= f[ia];
            #pragma acc atomic
            srhs[ADDR_(ia,iqr,nq)]+= f[ia];
        }
         sauxc[ADDR(nauxf-1,ic,nfc)]= tmpauxc;
     }
  }

   void cMfAusmUpGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                            cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                            cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             nql,nqr,iql,iqr,ic;

      Int nfc, nq;
      Int *sicql;
      Real *sql, *sauxl, *slhsl;
      Int *sicqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      sicql = icql.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      sicqr = icqr.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(             sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 sicqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= ic;
            iqr= sicqr[ADDR(0,ic,nfc)];

            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsl[ADDR_(0,iql,nql)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
  }

   void cMfAusmUpGas::ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                                           cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             iql,iqr,ic;

      Int nfc, nq;
      Int *sicq;
      Real *sq, *saux, *slhs, *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      slhs  = lhs.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {

        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],slhs[0:nlhs*nq], \
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this )\
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {

            iql= sicq[ADDR(0,ic,nfc)];
            iqr= sicq[ADDR(1,ic,nfc)];

            //lhsl[0][iql]+= auxc[nauxf-1][ic];
            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhs[ADDR_(0,iql,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];
            #pragma acc atomic
            slhs[ADDR_(0,iqr,nq)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->ilhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc ) ;
      vsc->ilhs( ics, ice, icq, q, aux, lhs, wc, wxdc, auxc ) ;
  }

   void cMfAusmUpGas::wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                            cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            gam1,kr;
      Real            dp1,dp2,dp3,dp4,dp5;
      Real            dfr[MxNVs][MxNVs];

      Int             ic,ia,iql,iqr,ja;
      Int             nql,nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *slhsl;
      Int *icqr;
      Real *sqr, *sauxr, *slhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq  = qr.get_dim1();

      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      slhsl = lhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data();
      sauxr = auxr.get_data();
      slhsr = lhsr.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

      if( ice > ics )
     {
         nql = nfc;
         nqr = nq;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(            sql[0:nv*nfc],sauxl[0:naux*nfc],slhsl[0:nlhs*nfc],\
                 icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], slhsr[0:nlhs*nq],\
                 swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
         default(none)
         for( ic=ics;ic<ice;ic++ )
        {
            iql= ic;
            iqr= icqr[ic];

            //lhsr[0][iqr]+= auxc[nauxf-1][ic];
            #pragma acc atomic
            slhsr[ADDR_(0,iqr,nqr)]+= sauxc[ADDR_((nauxf-1),ic,nfc)];

        }
        #pragma acc exit data delete(this)
     }
      //vsc->wlhs( ics, ice, icql, ql, auxl, lhsl, icqr, qr, auxr, lhsr, wc, wxdc, auxc );
      vsc->wlhs( ics, ice, icql_view, ql, auxl, lhsl, icqr_view, qr, auxr, lhsr, wc, wxdc, auxc );
  }

   void cMfAusmUpGas::slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa )
  {
      //vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhs );
      vsc->slhs( iqs,iqe, cfl, q,aux, dqdx,dst,wq,lhsa );
  }

   void cMfAusmUpGas::vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                          cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord )
  {
      Int             ia,ja,iq;
      Real            w,tau;

      Int nq;
      Real *sq, *saux, *sdqdx, *sdst, *swq, *slhs;

      nq    = q.get_dim1();

      sq    = q.get_data();
      saux  = aux.get_data();
      sdqdx = dqdx.get_data();
      sdst  = dst.get_data();
      swq   = wq.get_data();
      slhs  = lhs.get_data();

      if( iqe > iqs )
     {
         w= 1./cfl;
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop \
         present(sq[0:nv*nq],saux[0:naux*nq],sdqdx[0:nx*nv*nq],sdst[0:2*nq],swq[0:(nx+1)*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            //lhs[nlhs-1][iq]=  lhs[0][iq]*w;
            //if(rord>0) lhs[nlhs-1][iq]+= rord*wq[0][iq]/dtm;
            //lhs[0][iq]+=     lhs[nlhs-1][iq];
            slhs[ADDR(nlhs-1,iq,nq)]=  slhs[ADDR(0,iq,nq)]*w;
            if(rord>0) slhs[ADDR(nlhs-1,iq,nq)]+= rord*swq[ADDR(0,iq,nq)]/dtm;
            slhs[ADDR(0,iq,nq)]+=     slhs[ADDR(nlhs-1,iq,nq)];

        }
        #pragma acc exit data delete(this)
     }
      vsc->vlhs( iqs,iqe, cfl, wq,lhs );
  }

   void cMfAusmUpGas::invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res )
  {
      Int iv,iq;

      Int nq;
      Real *slhs, *sres;

      nq = lhs.get_dim1();
      slhs = lhs.get_data();
      sres = res.get_data();

      if( iqe > iqs )
     {
        #pragma acc enter data copyin(this)
        #pragma acc parallel loop gang vector\
         present(sres[0:nv*nq],slhs[0:nlhs*nq],this)\
         default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            for( iv=0;iv<nv0;iv++ )
           {
               sres[ADDR(iv,iq,nq)]/= slhs[ADDR(0,iq,nq)];
           }
        }
        #pragma acc exit data copyout(this)
     }
      //vsc->invdg( iqs,iqe, lhs,res );
      vsc->invdg( iqs,iqe, lhs,res );
  }


   void cMfAusmUpGas::dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view )
  {
      Int iq,ia;
      Real h,t,p,ro,dt,dp,dk,re,dro,dre,cv,e;
      Int nq;
      Real *q, *aux, *dU, *dq;

      nq = q_view.get_dim1();

      q = q_view.get_data();
      aux = aux_view.get_data();
      dU = dU_view.get_data();
      dq = dq_view.get_data();

      cv= rg/(gam-1.);

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop \
      present(q[0:nv*nq],aux[0:naux*nq],dU[0:nv*nq],dq[0:nv*nq],this) \
      default(none)
      for( iq=iqs;iq<iqe;iq++ )
     {
         //t=  q[3][iq];
         //p=  q[4][iq];
         //ro= aux[0][iq];
         //h=  aux[3][iq];
         t=  q[ADDR(3,iq,nq)];
         p=  q[ADDR(4,iq,nq)];
         ro= aux[ADDR(0,iq,nq)];
         h=  aux[ADDR(3,iq,nq)];
         re= ro*h- p;
         e= re/ro;
         //dro= dU[0][iq];
         //dre= dU[4][iq];
         dro= dU[ADDR(0,iq,nq)];
         dre= dU[ADDR(4,iq,nq)];

         //dq[0][iq]= ( dU[1][iq]- q[0][iq]*dro )/ro;
         //dq[1][iq]= ( dU[2][iq]- q[1][iq]*dro )/ro;
         //dq[2][iq]= ( dU[3][iq]- q[2][iq]*dro )/ro;
         dq[ADDR(0,iq,nq)]= ( dU[ADDR(1,iq,nq)]- q[ADDR(0,iq,nq)]*dro )/ro;
         dq[ADDR(1,iq,nq)]= ( dU[ADDR(2,iq,nq)]- q[ADDR(1,iq,nq)]*dro )/ro;
         dq[ADDR(2,iq,nq)]= ( dU[ADDR(3,iq,nq)]- q[ADDR(2,iq,nq)]*dro )/ro;

         //dk=  q[0][iq]*dq[0][iq];
         //dk+= q[1][iq]*dq[1][iq];
         //dk+= q[2][iq]*dq[2][iq];
         dk=  q[ADDR(0,iq,nq)]*dq[ADDR(0,iq,nq)];
         dk+= q[ADDR(1,iq,nq)]*dq[ADDR(1,iq,nq)];
         dk+= q[ADDR(2,iq,nq)]*dq[ADDR(2,iq,nq)];

         dk*= ro;
         dt= ( dre- e*dro- dk )/( ro*cv );
         dp= p*( dro/ro+ dt/t );

         //dq[3][iq]= dt;
         //dq[4][iq]= dp;
         dq[ADDR(3,iq,nq)]= dt;
         dq[ADDR(4,iq,nq)]= dp;
     }
     #pragma acc exit data delete(this)
  }

   void cMfAusmUpGas::auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch )
  {
      Int iq;
      Int iv;
      Real            mu=1.85e-5,kappa=2.624e-2,pr=0.7;

      mu/= unit[0];
      kappa/= (unit[0]*unit[0]*unit[0]);
      Real cp=rg*gam/(gam-1);

      if(arch=="d")
     {
         Int nq;
         Real *q, *aux;

         nq = q_view.get_dim1();

         q   = q_view.get_data();
         aux = aux_view.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(q[0:nv*nq],aux[0:naux*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux[ADDR(0,iq,nq)]= q[ADDR(4,iq,nq)]/( rg*q[ADDR(3,iq,nq)] );
//    kinetic energy
            aux[ADDR(1,iq,nq)]=  q[ADDR(0,iq,nq)]*q[ADDR(0,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(1,iq,nq)]*q[ADDR(1,iq,nq)];
            aux[ADDR(1,iq,nq)]+= q[ADDR(2,iq,nq)]*q[ADDR(2,iq,nq)];
            aux[ADDR(1,iq,nq)]*= 0.5;
//    speed of sound and total entalpy
            aux[ADDR(2,iq,nq)]= gam*rg* q[ADDR(3,iq,nq)];
            aux[ADDR(3,iq,nq)]= aux[ADDR(2,iq,nq)]/(gam-1)+ aux[ADDR(1,iq,nq)];
            aux[ADDR(2,iq,nq)]= sqrt( aux[ADDR(2,iq,nq)] );
            aux[ADDR(4,iq,nq)]= cp;
            aux[ADDR(5,iq,nq)]= mu;
            aux[ADDR(6,iq,nq)]= kappa;
        }
         #pragma acc exit data delete(this)
     }
      else if(arch=="h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
//    density
            aux_view(0,iq)= q_view(4,iq)/( rg*q_view(3,iq) );
//    kinetic energy
            aux_view(1,iq)=  q_view(0,iq)*q_view(0,iq);
            aux_view(1,iq)+= q_view(1,iq)*q_view(1,iq);
            aux_view(1,iq)+= q_view(2,iq)*q_view(2,iq);
            aux_view(1,iq)*= 0.5;
//    speed of sound and total entalpy
            aux_view(2,iq)= gam*rg* q_view(3,iq);
            aux_view(3,iq)= aux_view(2,iq)/(gam-1)+ aux_view(1,iq);
            aux_view(2,iq)= sqrt( aux_view(2,iq) );
            aux_view(4,iq)= cp;
            aux_view(5,iq)= mu;
            aux_view(6,iq)= kappa;
        }
     }
      else
     {
         cout << "unkown arch\n";
         assert(0);
     }
  }

   void cMfAusmUpGas::cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch )
  {
      Int iq,ia;
      Int nq;
      Real *sq, *saux, *sqo;

      if(arch == "d")
     {
         nq = q.get_dim1();

         sq   = q.get_data();
         saux = aux.get_data();
         sqo  = qo.get_data();

         #pragma acc enter data copyin(this)
         #pragma acc parallel loop \
          present(sq[0:nv*nq],saux[0:naux*nq],sqo[0:nv*nq],this) \
          default(none)
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            sqo[ADDR(0,iq,nq)]= saux[ADDR(0,iq,nq)];
            sqo[ADDR(1,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(0,iq,nq)];
            sqo[ADDR(2,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(1,iq,nq)];
            sqo[ADDR(3,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(2,iq,nq)];
            sqo[ADDR(4,iq,nq)]= sqo[ADDR(0,iq,nq)]*saux[ADDR(3,iq,nq)]- sq[ADDR(4,iq,nq)];
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               sqo[ADDR(ia,iq,nq)]= sqo[ADDR(0,iq,nq)]*sq[ADDR(ia,iq,nq)];
           }
        }
         #pragma acc exit data delete(this)
     }
      else if(arch == "h")
     {
         for( iq=iqs;iq<iqe;iq++ )
        {
            // density
            qo(0,iq)= aux(0,iq);
            qo(1,iq)= qo(0,iq)*q(0,iq);
            qo(2,iq)= qo(0,iq)*q(1,iq);
            qo(3,iq)= qo(0,iq)*q(2,iq);
            qo(4,iq)= qo(0,iq)*aux(3,iq)- q(4,iq);
            for( ia=5;ia<nv;ia++ )
           {
               //qo[ia][iq]= qo[0][iq]*q[ia][iq];
               qo(ia,iq)= qo(0,iq)*q(ia,iq);
           }
        }
     }
      else
     {
         assert(0);
     }
  }

   void cMfAusmUpGas::wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,  
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real f[MxNVs],wn[4];
      Int  ic,iql,iqr,nql,nqr;

      Int nfc, nq;    
      Int *icql;      
      Real *sql, *sauxl, *srhsl;
      Int *icqr;
      Real *sqr, *sauxr, *srhsr;
      Real *swc, *swxdc, *sauxc;

      nfc = wc.get_dim1();
      nq = qr.get_dim1();
  
      icql  = icql_view.get_data();
      sql   = ql.get_data();
      sauxl = auxl.get_data();
      srhsl = rhsl.get_data();
      icqr  = icqr_view.get_data();
      sqr   = qr.get_data(); 
      sauxr = auxr.get_data(); 
      srhsr = rhsr.get_data();
      swc   = wc.get_data(); 
      swxdc = wxdc.get_data(); 
      sauxc = auxc.get_data();

      nql = nfc;
      nqr = nq;
      #pragma acc enter data copyin(this)
      #pragma acc parallel loop \
       firstprivate(nql,nqr) \
       private(f,wn)\
       present(            sql[0:nv*nfc],sauxl[0:naux*nfc],srhsl[0:nv*nfc],\
               icqr[0:nfc],sqr[0:nv*nq], sauxr[0:naux*nq], srhsr[0:nv*nq],\
               swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this) \
       default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left
         //f[1]=    wc[0][ic]* qr[4][iqr]* wc[3][ic];
         //f[2]=    wc[1][ic]* qr[4][iqr]* wc[3][ic];
         //f[3]=    wc[2][ic]* qr[4][iqr]* wc[3][ic];
         //f[4]=  wxdc[0][ic]* qr[4][iqr]* wc[3][ic];
         f[1]=    wn[0]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[2]=    wn[1]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[3]=    wn[2]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         f[4]=  swxdc[ADDR(0,ic,nfc)]* sqr[ADDR(4,iqr,nqr)]* wn[3];
         //auxc[nauxf-1][ic]= ( auxr[2][iqr]+fabs(wxdc[0][ic]) )*wc[3][ic];
         sauxc[ADDR(nauxf-1,ic,nfc)]= ( sauxr[ADDR(2,iqr,nqr)]+fabs(swxdc[ADDR(0,ic,nfc)]) )*wn[3];

         //rhsl[1][iql]-= f[1];
         //rhsl[2][iql]-= f[2];
         //rhsl[3][iql]-= f[3];
         //rhsl[4][iql]-= f[4];
         #pragma acc atomic
         srhsl[ADDR_(1,iql,nql)]-= f[1];
         #pragma acc atomic
         srhsl[ADDR_(2,iql,nql)]-= f[2];
         #pragma acc atomic
         srhsl[ADDR_(3,iql,nql)]-= f[3];
         #pragma acc atomic
         srhsl[ADDR_(4,iql,nql)]-= f[4];

         #pragma acc atomic
         srhsr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         srhsr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         srhsr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         srhsr[ADDR_(4,iqr,nqr)]+= f[4];
     }
  }

   void cMfAusmUpGas::dwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Real            dpr;

      Int             ia,ic,iql,iqr;
      Real            f[MxNVs],wn[4];
      Int             nfc,nqr;

      Int nbb, nq;
      Int *icql; 
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr; 
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;

      nbb = wc.get_dim1();
      nq  = qr.get_dim1();

      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nfc = nbb;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      private(f,wn)\
      present(sql[0:nv*nfc],sauxl[0:naux*nfc],sdql[0:nv*nfc],sdauxl[0:nv*nfc],sresl[0:nv*nfc],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic];

         wn[0] = swc[ADDR(0,ic,nfc)];
         wn[1] = swc[ADDR(1,ic,nfc)];
         wn[2] = swc[ADDR(2,ic,nfc)];
         wn[3] = swc[ADDR(3,ic,nfc)];
         //dpr=  dauxr[4][iqr];
         //f[1]=   wc[0][ic]*dpr*wc[3][ic];
         //f[2]=   wc[1][ic]*dpr*wc[3][ic];
         //f[3]=   wc[2][ic]*dpr*wc[3][ic];
         //f[4]= wxdc[0][ic]*dpr*wc[3][ic];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         f[1]=   wn[0]*dpr*wn[3];
         f[2]=   wn[1]*dpr*wn[3];
         f[3]=   wn[2]*dpr*wn[3];
         f[4]= swxdc[ADDR(0,ic,nfc)]*dpr*wn[3];

         //resl[1][iql]-= f[1];
         //resl[2][iql]-= f[2];
         //resl[3][iql]-= f[3];
         //resl[4][iql]-= f[4];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nfc)]-= f[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nfc)]-= f[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nfc)]-= f[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nfc)]-= f[4];

         //resr[1][iqr]+= f[1];
         //resr[2][iqr]+= f[2];
         //resr[3][iqr]+= f[3];
         //resr[4][iqr]+= f[4];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= f[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= f[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= f[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= f[4];

     }
     #pragma acc exit data copyout(this)
  }

   void cMfAusmUpGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ia,ic,iql,iqr;
      Real            al,unl,rl,pl,hl,dfl[MxNVs],als,ml,mlp,plp, drl,dpl,drel,drhl,dpnl,dml,dmlp,dplp,dunl;
      Real            ar,unr,rr,pr,hr,dfr[MxNVs],ars,mr,mrm,prm, drr,dpr,drer,drhr,dpnr,dmr,dmrm,dprm,dunr;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            dm,dmp,dmm,dm2p,dm2m;
      Real            df[MxNVs], wn[4];

      Int             nql, nqr;

      Int nfc, nq;
      Int *icql;
      Real *sql, *sauxl, *sdql, *sdauxl, *sresl;
      Int *icqr;
      Real *sqr, *sauxr, *sdqr, *sdauxr, *sresr;
      Real *swc, *swxdc, *sauxc;
         
      nfc = wc.get_dim1();
      nq  = qr.get_dim1();
         
      icql   = icql_view.get_data();
      sql    = ql.get_data();
      sauxl  = auxl.get_data();
      sdql   = dql.get_data();
      sdauxl = dauxl.get_data();
      sresl  = resl.get_data();
      icqr   = icqr_view.get_data();
      sqr    = qr.get_data();
      sauxr  = auxr.get_data();
      sdqr   = dqr.get_data();
      sdauxr = dauxr.get_data();
      sresr  = resr.get_data();
      swc    = wc.get_data();
      swxdc  = wxdc.get_data();
      sauxc  = auxc.get_data();

      nql = nfc;
      nqr = nq;
     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(nql,nqr), \
      private(dfl,dfr,df,wn)\
      present(sql[0:nv*nql],sauxl[0:naux*nql],sdql[0:nv*nql],sdauxl[0:nv*nql],sresl[0:nv*nql],\
              icqr[0:nfc],sqr[0:nv*nqr],sauxr[0:naux*nqr],sdqr[0:nv*nqr],sdauxr[0:nv*nqr],sresr[0:nv*nqr],\
              swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= ic;
         iqr= icqr[ic]; 

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         //al= auxl[2][iql];
         pl= sql[ADDR(4,iql,nql)];
         rl= sauxl[ADDR(0,iql,nql)];
         hl= sauxl[ADDR(3,iql,nql)];
         al= sauxl[ADDR(2,iql,nql)];

         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdql[ADDR(0,iql,nql)];
         dpl=  sdauxl[ADDR(4,iql,nql)];
         drel=   sdql[ADDR(4,iql,nql)];
         drhl=   drel+ dpl;

         //unl=  wc[0][ic]*ql[0][iql];
         //unl+= wc[1][ic]*ql[1][iql];
         //unl+= wc[2][ic]*ql[2][iql];
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sql[ADDR(0,iql,nql)];
         unl+= wn[1]*sql[ADDR(1,iql,nql)];
         unl+= wn[2]*sql[ADDR(2,iql,nql)];
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdauxl[ADDR(0,iql,nql)]; 
         dunl+= wn[1]*sdauxl[ADDR(1,iql,nql)]; 
         dunl+= wn[2]*sdauxl[ADDR(2,iql,nql)]; 

         als = 2.*( gam-1. )*hl/( gam+1. );
         als = sqrt(als);
         als = als*als/max(als,unl);

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         //ar= auxr[2][iqr];
         pr= sqr[ADDR(4,iqr,nqr)];
         rr= sauxr[ADDR(0,iqr,nqr)];
         hr= sauxr[ADDR(3,iqr,nqr)];
         ar= sauxr[ADDR(2,iqr,nqr)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdqr[ADDR(0,iqr,nqr)];
         dpr=  sdauxr[ADDR(4,iqr,nqr)];
         drer=   sdqr[ADDR(4,iqr,nqr)];
         drhr=   drer+ dpr;

         //unr=  wc[0][ic]*qr[0][iqr];
         //unr+= wc[1][ic]*qr[1][iqr];
         //unr+= wc[2][ic]*qr[2][iqr];
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sqr[ADDR(0,iqr,nqr)];
         unr+= wn[1]*sqr[ADDR(1,iqr,nqr)];
         unr+= wn[2]*sqr[ADDR(2,iqr,nqr)];
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdauxr[ADDR(0,iqr,nqr)]; 
         dunr+= wn[1]*sdauxr[ADDR(1,iqr,nqr)]; 
         dunr+= wn[2]*sdauxr[ADDR(2,iqr,nqr)]; 

         ars = 2.*( gam-1. )*hr/( gam+1. );
         ars = sqrt(ars);
         ars = ars*ars/max(ars,-unr);

         as= min( ars,als );

// linearised AUSM+ flux - 2 coordinates, 2 velocity components


         ml  = unl/as;
         mr  = unr/as;
         dml = dunl/as;
         dmr = dunr/as;

         if( ml < -1. )
        {
            mlp= 0.;
            plp= 0.;
            dmlp= 0.;
            dplp= 0.;
        }
         else 
        {
            if( ml < 1. )
           {
               m2p= ml+1;
               dm2p= 0.5*m2p*dml;
               m2p= 0.25*m2p*m2p;
               m2m= ml-1;
               dm2m=-0.5*m2m*dml;
               m2m=-0.25*m2m*m2m;
               mlp= m2p*( 1- 16*beta0*m2m );
               plp= m2p*( (2-ml)-16*alpha0*ml*m2m );
               dmlp= dm2p*( 1.-16.*beta0*m2m )- 16.*beta0*m2p*dm2m;
               dplp= dm2p*( 2.-ml-16.*alpha0*ml*m2m )+ m2p*(  -dml-16.*alpha0*( dml*m2m+ ml*dm2m ) );
           }
            else
           {
               mlp= ml;
               plp= 1.;
               dmlp= dml;
               dplp= 0.;
           }
        }

         if( mr < -1. ) 
        {
            mrm= mr;
            prm= 1.;
            dmrm= dmr;
            dprm= 0.;

        }
         else 
        {
            if( mr < 1. )
           {
               m2p= mr+1;
               dm2p= 0.5*m2p*dmr;
               m2p= 0.25*m2p*m2p;
               m2m= mr-1;
               dm2m=-0.5*m2m*dmr;
               m2m=-0.25*m2m*m2m;
               mrm= m2m*( 1+ 16*beta0*m2p );
               prm=-m2m*( 2+mr-16*alpha0*mr*m2p );
               dmrm= dm2m*( 1.+ 16.*beta0*m2p )+ 16.*beta0*m2m*dm2p;
               dprm=-dm2m*( 2.+mr-16.*alpha0*mr*m2p )- m2m*(   dmr-16.*alpha0*( dmr*m2p+ mr*dm2p ) );
           }
            else
           {
               mrm= 0.;
               prm= 0.;
               dmrm= 0.;
               dprm= 0.;
           } 
        }

         m= as*( mlp+ mrm );
         dm= as*( dmlp+ dmrm );
         if( m > 0 )
        {
            mp= m;
            dmp= dm;
            mm= 0;
            dmm= 0;
        }
         else
        {
            mp= 0;
            dmp= 0;
            mm= m;
            dmm= dm;
        }

         dpnl= plp*dpl+ dplp*pl;
         dpnr= prm*dpr+ dprm*pr;

// assemble 
         dfl[0]= dmp*rl+            mp*drl;
         //dfl[1]= dmp*rl*ql[0][iql]+ mp*dql[1][iql]+ dpnl*wc[0][ic];
         //dfl[2]= dmp*rl*ql[1][iql]+ mp*dql[2][iql]+ dpnl*wc[1][ic];
         //dfl[3]= dmp*rl*ql[2][iql]+ mp*dql[3][iql]+ dpnl*wc[2][ic];
         dfl[1]= dmp*rl*sql[ADDR(0,iql,nql)]+ mp*sdql[ADDR(1,iql,nql)]+ dpnl*wn[0];
         dfl[2]= dmp*rl*sql[ADDR(1,iql,nql)]+ mp*sdql[ADDR(2,iql,nql)]+ dpnl*wn[1];
         dfl[3]= dmp*rl*sql[ADDR(2,iql,nql)]+ mp*sdql[ADDR(3,iql,nql)]+ dpnl*wn[2];
         dfl[4]= dmp*rl*hl+         mp*drhl + dpnl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
             //dfl[ia]= dmp*rl*ql[ia][iql]+ mp*dql[ia][iql]; 
             dfl[ia]= dmp*rl*sql[ADDR(ia,iql,nql)]+ mp*sdql[ADDR(ia,iql,nql)]; 
        }

         dfr[0]= dmm*rr+            mm*drr;
         //dfr[1]= dmm*rr*qr[0][iqr]+ mm*dqr[1][iqr]+ dpnr*wc[0][ic];
         //dfr[2]= dmm*rr*qr[1][iqr]+ mm*dqr[2][iqr]+ dpnr*wc[1][ic];
         //dfr[3]= dmm*rr*qr[2][iqr]+ mm*dqr[3][iqr]+ dpnr*wc[2][ic];
         dfr[1]= dmm*rr*sqr[ADDR(0,iqr,nqr)]+ mm*sdqr[ADDR(1,iqr,nqr)]+ dpnr*wn[0];
         dfr[2]= dmm*rr*sqr[ADDR(1,iqr,nqr)]+ mm*sdqr[ADDR(2,iqr,nqr)]+ dpnr*wn[1];
         dfr[3]= dmm*rr*sqr[ADDR(2,iqr,nqr)]+ mm*sdqr[ADDR(3,iqr,nqr)]+ dpnr*wn[2];
         dfr[4]= dmm*rr*hr+         mm*drhr + dpnr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
             //dfr[ia]= dmm*rr*qr[ia][iqr]+ mm*dqr[ia][iqr]; 
             dfr[ia]= dmm*rr*sqr[ADDR(ia,iqr,nqr)]+ mm*sdqr[ADDR(ia,iqr,nqr)]; 
        }

         df[0]= ( dfl[0]+ dfr[0] )*wn[3];
         df[1]= ( dfl[1]+ dfr[1] )*wn[3];
         df[2]= ( dfl[2]+ dfr[2] )*wn[3];
         df[3]= ( dfl[3]+ dfr[3] )*wn[3];
         df[4]= ( dfl[4]+ dfr[4] )*wn[3];
         for( ia=5;ia<nv;ia++ )
        {
             //df[ia]= ( dfl[ia]+ dfr[ia] )*wc[3][ic];
             df[ia]= ( dfl[ia]+ dfr[ia] )*wn[3];
        }

         #pragma acc atomic
         sresl[ADDR_(0,iql,nql)]-= df[0];
         #pragma acc atomic
         sresl[ADDR_(1,iql,nql)]-= df[1];
         #pragma acc atomic
         sresl[ADDR_(2,iql,nql)]-= df[2];
         #pragma acc atomic
         sresl[ADDR_(3,iql,nql)]-= df[3];
         #pragma acc atomic
         sresl[ADDR_(4,iql,nql)]-= df[4];

         #pragma acc atomic
         sresr[ADDR_(0,iqr,nqr)]+= df[0];
         #pragma acc atomic
         sresr[ADDR_(1,iqr,nqr)]+= df[1];
         #pragma acc atomic
         sresr[ADDR_(2,iqr,nqr)]+= df[2];
         #pragma acc atomic
         sresr[ADDR_(3,iqr,nqr)]+= df[3];
         #pragma acc atomic
         sresr[ADDR_(4,iqr,nqr)]+= df[4];

         for( ia=5;ia<nv;ia++ )
        {
            //resl[ia][iql]-= df[ia];
            //resr[ia][iqr]+= df[ia];
            #pragma acc atomic
            sresl[ADDR_(ia,iql,nql)]-= df[ia];
            #pragma acc atomic
            sresr[ADDR_(ia,iqr,nqr)]+= df[ia];
        }
     }
  }

   void cMfAusmUpGas::diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      Int             ia,ic,iql,iqr;
      Real            al,unl,rl,pl,hl,dfl[MxNVs],als,ml,mlp,plp, drl,dpl,drel,drhl,dpnl,dml,dmlp,dplp,dunl;
      Real            ar,unr,rr,pr,hr,dfr[MxNVs],ars,mr,mrm,prm, drr,dpr,drer,drhr,dpnr,dmr,dmrm,dprm,dunr;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            dm,dmp,dmm,dm2p,dm2m;
      Real            df[MxNVs], wn[4];

      Int nfc, nq;

      nfc = wc.get_dim1();
      nq  = q.get_dim1();

      Int *sicq;
      Real *sq, *saux, *sdq, *sdaux, *sres, *swc, *swxdc, *sauxc;

      sicq  = icq.get_data();
      sq    = q.get_data();
      saux  = aux.get_data();
      sdq   = dq0.get_data();
      sdaux = daux.get_data();
      sres  = res.get_data();
      swc   = wc.get_data();
      swxdc = wxdc.get_data();
      sauxc = auxc.get_data();

     #pragma acc enter data copyin(this)
     #pragma acc parallel loop gang vector\
      firstprivate(cp), \
      private(dfl,dfr,df,wn)\
      present(sicq[0:2*nfc],sq[0:nv*nq],saux[0:naux*nq],sdq[0:nv*nq],sdaux[0:nv*nq],\
               sres[0:nv*nq],swc[0:(nx+1)*nfc],swxdc[0:nfc],sauxc[0:nauxf*nfc],this ) \
      default(none)
      for( ic=ics;ic<ice;ic++ )
     {
         iql= sicq[ADDR(0,ic,nfc)];
         iqr= sicq[ADDR(1,ic,nfc)];

         wn[0]= swc[ADDR(0,ic,nfc)];
         wn[1]= swc[ADDR(1,ic,nfc)];
         wn[2]= swc[ADDR(2,ic,nfc)];
         wn[3]= swc[ADDR(3,ic,nfc)];

// fluxes from the left

         //pl= ql[4][iql];
         //rl= auxl[0][iql];
         //hl= auxl[3][iql];
         //al= auxl[2][iql];
         pl= sq[ADDR(4,iql,nq)];
         rl= saux[ADDR(0,iql,nq)];
         hl= saux[ADDR(3,iql,nq)];
         al= saux[ADDR(2,iql,nq)];

         //drl=    dql[0][iql];
         //dpl=  dauxl[4][iql];
         //drel=   dql[4][iql];
         drl=    sdq[ADDR(0,iql,nq)];
         dpl=  sdaux[ADDR(4,iql,nq)];
         drel=   sdq[ADDR(4,iql,nq)];
         drhl=   drel+ dpl;

         //unl=  wc[0][ic]*ql[0][iql];
         //unl+= wc[1][ic]*ql[1][iql];
         //unl+= wc[2][ic]*ql[2][iql];
         //unl-= wxdc[0][ic];
         unl=  wn[0]*sq[ADDR(0,iql,nq)];
         unl+= wn[1]*sq[ADDR(1,iql,nq)];
         unl+= wn[2]*sq[ADDR(2,iql,nq)];
         unl-= swxdc[ADDR(0,ic,nfc)];

         //dunl=  wc[0][ic]*dauxl[0][iql]; 
         //dunl+= wc[1][ic]*dauxl[1][iql]; 
         //dunl+= wc[2][ic]*dauxl[2][iql]; 
         dunl=  wn[0]*sdaux[ADDR(0,iql,nq)]; 
         dunl+= wn[1]*sdaux[ADDR(1,iql,nq)]; 
         dunl+= wn[2]*sdaux[ADDR(2,iql,nq)]; 

         als = 2.*( gam-1. )*hl/( gam+1. );
         als = sqrt(als);
         als = als*als/max(als,unl);

// fluxes from the right

         //pr= qr[4][iqr];
         //rr= auxr[0][iqr];
         //hr= auxr[3][iqr];
         //ar= auxr[2][iqr];
         pr= sq[ADDR(4,iqr,nq)];
         rr= saux[ADDR(0,iqr,nq)];
         hr= saux[ADDR(3,iqr,nq)];
         ar= saux[ADDR(2,iqr,nq)];

         //drr=    dqr[0][iqr];
         //dpr=  dauxr[4][iqr];
         //drer=   dqr[4][iqr];
         drr=    sdq[ADDR(0,iqr,nq)];
         dpr=  sdaux[ADDR(4,iqr,nq)];
         drer=   sdq[ADDR(4,iqr,nq)];
         drhr=   drer+ dpr;

         //unr=  wc[0][ic]*qr[0][iqr];
         //unr+= wc[1][ic]*qr[1][iqr];
         //unr+= wc[2][ic]*qr[2][iqr];
         //unr-= wxdc[0][ic];
         unr=  wn[0]*sq[ADDR(0,iqr,nq)];
         unr+= wn[1]*sq[ADDR(1,iqr,nq)];
         unr+= wn[2]*sq[ADDR(2,iqr,nq)];
         unr-= swxdc[ADDR(0,ic,nfc)];

         //dunr=  wc[0][ic]*dauxr[0][iqr]; 
         //dunr+= wc[1][ic]*dauxr[1][iqr]; 
         //dunr+= wc[2][ic]*dauxr[2][iqr]; 
         dunr=  wn[0]*sdaux[ADDR(0,iqr,nq)]; 
         dunr+= wn[1]*sdaux[ADDR(1,iqr,nq)]; 
         dunr+= wn[2]*sdaux[ADDR(2,iqr,nq)]; 

         ars = 2.*( gam-1. )*hr/( gam+1. );
         ars = sqrt(ars);
         ars = ars*ars/max(ars,-unr);

         //as= min( ars,als );
         as= 0.5*(al+ar);

// linearised AUSM+ flux - 2 coordinates, 2 velocity components

         ml  = unl/as;
         mr  = unr/as;
         dml = dunl/as;
         dmr = dunr/as;

         m= 0.5*( ml*ml+ mr*mr );
         Real m0= min( 1.,max(m,minf*minf) );
         m0= sqrt(m0);
         Real fa= m0*( 2- m0 );

         Real alpha= alpha0*( 5*fa*fa- 4 );

         if( ml < -1. )
        {
            mlp= 0.;
            plp= 0.;
            dmlp= 0.;
            dplp= 0.;
        }
         else 
        {
            if( ml < 1. )
           {
               m2p= ml+1;
               dm2p= 0.5*m2p*dml;
               m2p= 0.25*m2p*m2p;
               m2m= ml-1;
               dm2m=-0.5*m2m*dml;
               m2m=-0.25*m2m*m2m;
               mlp= m2p*( 1- 16*beta0*m2m );
               plp= m2p*( (2-ml)-16*alpha*ml*m2m );
               dmlp= dm2p*( 1.-16.*beta0*m2m )- 16.*beta0*m2p*dm2m;
               dplp= dm2p*( 2.-ml-16.*alpha*ml*m2m )+ m2p*(  -dml-16.*alpha*( dml*m2m+ ml*dm2m ) );
           }
            else
           {
               mlp= ml;
               plp= 1.;
               dmlp= dml;
               dplp= 0.;
           }
        }

         if( mr < -1. ) 
        {
            mrm= mr;
            prm= 1.;
            dmrm= dmr;
            dprm= 0.;

        }
         else 
        {
            if( mr < 1. )
           {
               m2p= mr+1;
               dm2p= 0.5*m2p*dmr;
               m2p= 0.25*m2p*m2p;
               m2m= mr-1;
               dm2m=-0.5*m2m*dmr;
               m2m=-0.25*m2m*m2m;
               mrm= m2m*( 1+ 16*beta0*m2p );
               prm=-m2m*( 2+mr-16*alpha*mr*m2p );
               dmrm= dm2m*( 1.+ 16.*beta0*m2p )+ 16.*beta0*m2m*dm2p;
               dprm=-dm2m*( 2.+mr-16.*alpha*mr*m2p )- m2m*(   dmr-16.*alpha*( dmr*m2p+ mr*dm2p ) );
           }
            else
           {
               mrm= 0.;
               prm= 0.;
               dmrm= 0.;
               dprm= 0.;
           } 
        }

         Real ra= 0.5*( rr+ rl );
         Real ma= -kp*fmax( 1-sigma0*m,0. )*( pr-pl )/( fa*as*as*ra );
         Real dma= -kp*fmax( 1-sigma0*m,0. )*( dpr-dpl )/( fa*as*as*ra );
         Real pa= -ku*plp*prm*2*ra*fa*as*( unr-unl );
         Real dpa= -ku*plp*prm*2*ra*fa*as*( dunr-dunl )-ku*(dplp*prm+plp*dprm)*2*ra*fa*as*( unr-unl );

         m= as*( mlp+ mrm +ma);
         dm= as*( dmlp+ dmrm +dma);
         if( m > 0 )
        {
            mp= m;
            dmp= dm;
            mm= 0;
            dmm= 0;
        }
         else
        {
            mp= 0;
            dmp= 0;
            mm= m;
            dmm= dm;
        }

         dpnl= plp*dpl+ dplp*pl + 0.5*dpa;
         dpnr= prm*dpr+ dprm*pr + 0.5*dpa;

// assemble 
         dfl[0]= dmp*rl+            mp*drl;
         //dfl[1]= dmp*rl*ql[0][iql]+ mp*dql[1][iql]+ dpnl*wc[0][ic];
         //dfl[2]= dmp*rl*ql[1][iql]+ mp*dql[2][iql]+ dpnl*wc[1][ic];
         //dfl[3]= dmp*rl*ql[2][iql]+ mp*dql[3][iql]+ dpnl*wc[2][ic];
         dfl[1]= dmp*rl*sq[ADDR(0,iql,nq)]+ mp*sdq[ADDR(1,iql,nq)]+ dpnl*wn[0];
         dfl[2]= dmp*rl*sq[ADDR(1,iql,nq)]+ mp*sdq[ADDR(2,iql,nq)]+ dpnl*wn[1];
         dfl[3]= dmp*rl*sq[ADDR(2,iql,nq)]+ mp*sdq[ADDR(3,iql,nq)]+ dpnl*wn[2];
         dfl[4]= dmp*rl*hl+         mp*drhl + dpl*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
             //dfl[ia]= dmp*rl*ql[ia][iql]+ mp*dql[ia][iql]; 
             dfl[ia]= dmp*rl*sq[ADDR(ia,iql,nq)]+ mp*sdq[ADDR(ia,iql,nq)]; 
        }

         dfr[0]= dmm*rr+            mm*drr;
         //dfr[1]= dmm*rr*qr[0][iqr]+ mm*dqr[1][iqr]+ dpnr*wc[0][ic];
         //dfr[2]= dmm*rr*qr[1][iqr]+ mm*dqr[2][iqr]+ dpnr*wc[1][ic];
         //dfr[3]= dmm*rr*qr[2][iqr]+ mm*dqr[3][iqr]+ dpnr*wc[2][ic];
         dfr[1]= dmm*rr*sq[ADDR(0,iqr,nq)]+ mm*sdq[ADDR(1,iqr,nq)]+ dpnr*wn[0];
         dfr[2]= dmm*rr*sq[ADDR(1,iqr,nq)]+ mm*sdq[ADDR(2,iqr,nq)]+ dpnr*wn[1];
         dfr[3]= dmm*rr*sq[ADDR(2,iqr,nq)]+ mm*sdq[ADDR(3,iqr,nq)]+ dpnr*wn[2];
         dfr[4]= dmm*rr*hr+         mm*drhr + dpr*swxdc[ADDR(0,ic,nfc)];
         for( ia=5;ia<nv;ia++ )
        {
             //dfr[ia]= dmm*rr*qr[ia][iqr]+ mm*dqr[ia][iqr]; 
             dfr[ia]= dmm*rr*sq[ADDR(ia,iqr,nq)]+ mm*sdq[ADDR(ia,iqr,nq)]; 
        }

         df[0]= ( dfl[0]+ dfr[0] )*wn[3];
         df[1]= ( dfl[1]+ dfr[1] )*wn[3];
         df[2]= ( dfl[2]+ dfr[2] )*wn[3];
         df[3]= ( dfl[3]+ dfr[3] )*wn[3];
         df[4]= ( dfl[4]+ dfr[4] )*wn[3];
         for( ia=5;ia<nv;ia++ )
        {
             //df[ia]= ( dfl[ia]+ dfr[ia] )*wc[3][ic];
             df[ia]= ( dfl[ia]+ dfr[ia] )*wn[3];
        }

         #pragma acc atomic
         sres[ADDR_(0,iql,nq)]-= df[0];
         #pragma acc atomic
         sres[ADDR_(1,iql,nq)]-= df[1];
         #pragma acc atomic
         sres[ADDR_(2,iql,nq)]-= df[2];
         #pragma acc atomic
         sres[ADDR_(3,iql,nq)]-= df[3];
         #pragma acc atomic
         sres[ADDR_(4,iql,nq)]-= df[4];

         #pragma acc atomic
         sres[ADDR_(0,iqr,nq)]+= df[0];
         #pragma acc atomic
         sres[ADDR_(1,iqr,nq)]+= df[1];
         #pragma acc atomic
         sres[ADDR_(2,iqr,nq)]+= df[2];
         #pragma acc atomic
         sres[ADDR_(3,iqr,nq)]+= df[3];
         #pragma acc atomic
         sres[ADDR_(4,iqr,nq)]+= df[4];

         for( ia=5;ia<nv;ia++ )
        {
            //resl[ia][iql]-= df[ia];
            //resr[ia][iqr]+= df[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iql,nq)]-= df[ia];
            #pragma acc atomic
            sres[ADDR_(ia,iqr,nq)]+= df[ia];
        }
     }

  }

   void cMfAusmUpGas::diflxb33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc )
  {
      diflx33(  ics,ice,  icql_view,ql,auxl,dql,dauxl,resl, icqr_view,qr,auxr,dqr,dauxr,resr, wc,wxdc,auxc );
  }

   void cMfAusmUpGas::ausm_plus_up_flux( Real *ql, Real *auxl, Real *qr, Real *auxr, Real *wn, Real ugrid, Real *f, Real *tmpauxc )
  {
      Int             ia;
      Real            al,unl,rl,pl,hl,als,ml,mlp,plp,ml_mag;
      Real            ar,unr,rr,pr,hr,ars,mr,mrm,prm,mr_mag;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            psi[MxNVs];
      Real            ma,pa,ra,m0,fa,alpha;
      Real            m1p_l, m1m_r, m2p_l, m2p_r, m2m_l, m2m_r;


      pl= ql[4];
      rl= auxl[0];
      al= auxl[2];
      hl= auxl[3];

      unl=  wn[0]*ql[0];
      unl+= wn[1]*ql[1];
      unl+= wn[2]*ql[2];
      unl-= ugrid;

      als = 2.*( gam-1. )*hl/( gam+1. );
      als = sqrt(als);
      als = als*als/max(als,unl);

      pr= qr[4];
      rr= auxr[0];
      ar= auxr[2];
      hr= auxr[3];

      unr=  wn[0]*qr[0];
      unr+= wn[1]*qr[1];
      unr+= wn[2]*qr[2];
      unr-= ugrid;

      ars = 2.*( gam-1. )*hr/( gam+1. );
      ars = sqrt(ars);
      ars = ars*ars/max(ars,-unr);

      //as= min( ars,als );
      as= 0.5*(al+ar);

      ml  = unl/as;
      mr  = unr/as;

      m= 0.5*( ml*ml+ mr*mr );
      m0= min( 1.,max(m,minf*minf) );
      m0= sqrt(m0);
      fa= m0*( 2- m0 );

      alpha= alpha0*( 5*fa*fa- 4 );

      ml_mag = fabs(ml);
      mr_mag = fabs(mr);

      m1p_l = 0.5*(ml + ml_mag );
      m1m_r = 0.5*(mr - mr_mag );

      m2p_l = 0.25*pow(ml + 1.0,2);
      m2p_r = 0.25*pow(mr + 1.0,2);
      m2m_l = -0.25*pow(ml -1.0,2);
      m2m_r = -0.25*pow(mr-1.0,2);

      mlp = ((ml_mag >= 1.0) ? m1p_l : (m2p_l*(1.0-16.0*beta0*m2m_l)));
      mrm = ((mr_mag >= 1.0) ? m1m_r : (m2m_r*(1.0+16.0*beta0*m2p_r)));
      
      ra= 0.5*( rr+ rl );
      ma= -kp*fmax( 1-sigma0*m,0. )*( pr-pl )/( fa*as*as*ra );
      ma= ma*fmax((1.0-tanh(100*sqrt(m))*tanh(100*sqrt(m))),0);

      plp = ((ml_mag >= 1.0) ? (m1p_l/ml) : (m2p_l*(( 2.0-ml)-16.0*alpha*ml*m2m_l)));
      prm = ((mr_mag >= 1.0) ? (m1m_r/mr) : (m2m_r*((-2.0-mr)+16.0*alpha*mr*m2p_r)));
          
      pa= -ku*plp*prm*2*ra*fa*as*( unr-unl );
          
      m= mlp+ mrm+ ma;
      ps= plp*pl+ prm*pr+ pa;

      f[0] = 0.5*as*(m*(rr      +rl)      -fabs(m)*(rr   -rl))*wn[3];
      f[1] = 0.5*as*(m*(rr*qr[0]+rl*ql[0])-fabs(m)*(rr*qr[0]-rl*ql[0]))*wn[3];
      f[2] = 0.5*as*(m*(rr*qr[1]+rl*ql[1])-fabs(m)*(rr*qr[1]-rl*ql[1]))*wn[3];
      f[3] = 0.5*as*(m*(rr*qr[2]+rl*ql[2])-fabs(m)*(rr*qr[2]-rl*ql[2]))*wn[3];
      f[4] = 0.5*as*(m*(rr*hr   +rl*hl)   -fabs(m)*(rr*hr   -rl*hl))*wn[3];

      f[1] += ps*wn[0]*wn[3];
      f[2] += ps*wn[1]*wn[3];
      f[3] += ps*wn[2]*wn[3];
      f[4] += ps*ugrid*wn[3];

      for(ia=5; ia<nv; ia++)
     {
         f[ia] = 0.5*as*(m*(rr*qr[ia]+rl*ql[ia])-fabs(m)*(rr*qr[ia]-rl*ql[ia]))*wn[3];
     }

      as= fmax( as,fmax(al,ar) );
      *tmpauxc = (as+ fmax( fabs(unl),fabs(unr) ))*wn[3];
  }

/*   void cMfAusmUpGas::ausm_plus_up_flux( Real *ql, Real *auxl, Real *qr, Real *auxr, Real *wn, Real ugrid, Real *f, Real *tmpauxc )
  {
      Int             ia;
      Real            al,unl,rl,pl,hl,als,ml,mlp,plp;
      Real            ar,unr,rr,pr,hr,ars,mr,mrm,prm;
      Real            as,ps,m,mp,mm,m2p,m2m;
      Real            psi[MxNVs];
      Real            ma,pa,ra,m0,fa,alpha;

      pl= ql[4];
      rl= auxl[0];
      al= auxl[2];
      hl= auxl[3];

      unl=  wn[0]*ql[0];
      unl+= wn[1]*ql[1];
      unl+= wn[2]*ql[2];
      unl-= ugrid;

      als = 2.*( gam-1. )*hl/( gam+1. );
      als = sqrt(als);
      als = als*als/max(als,unl);

      pr= qr[4];
      rr= auxr[0];
      ar= auxr[2];
      hr= auxr[3];

      unr=  wn[0]*qr[0];
      unr+= wn[1]*qr[1];
      unr+= wn[2]*qr[2];
      unr-= ugrid;

      ars = 2.*( gam-1. )*hr/( gam+1. );
      ars = sqrt(ars);
      ars = ars*ars/max(ars,-unr);

      //as= min( ars,als );
      as= 0.5*(al+ar);

      ml  = unl/as;
      mr  = unr/as;

      m= 0.5*( ml*ml+ mr*mr );
      m0= min( 1.,max(m,minf*minf) );
      m0= sqrt(m0);
      fa= m0*( 2- m0 );

      alpha= alpha0*( 5*fa*fa- 4 );

      if( ml < -1. )
     {
         mlp= 0.;
         plp= 0.;
     }
      else 
     {
         if( ml < 1. )
        {
            m2p= ml+1;
            m2p= 0.25*m2p*m2p;
            m2m= ml-1;
            m2m=-0.25*m2m*m2m;
            mlp= m2p*( 1- 16*beta0*m2m );
            plp= m2p*( (2-ml)-16*alpha*ml*m2m );
        }
         else
        {
            mlp= ml;
            plp= 1.;
        }
     }

      if( mr < -1. ) 
     {
         mrm= mr;
         prm= 1.;
     }
      else 
     {
         if( mr < 1. )
        {
            m2p= mr+1;
            m2p= 0.25*m2p*m2p;
            m2m= mr-1;
            m2m=-0.25*m2m*m2m;
            mrm= m2m*( 1+ 16*beta0*m2p );
            prm=-m2m*( 2+mr-16*alpha*mr*m2p );
        }
         else
        {
            mrm= 0.;
            prm= 0.;
        } 
     }

      ra= 0.5*( rr+ rl );
      ma= 0;
      pa= 0;
      ma= -kp*fmax( 1-sigma0*m,0. )*( pr-pl )/( fa*as*as*ra );
      pa= -ku*plp*prm*2*ra*fa*as*( unr-unl );
      ma = ma*fmax((1.0-tanh(50*sqrt(m))*tanh(50*sqrt(m))),0);
      m= mlp+ mrm+ ma;

      if( m > 0 )
     {
         mp= m;
         mm= 0;
         psi[1]= ql[0];
         psi[2]= ql[1];
         psi[3]= ql[2];
         psi[4]= hl;
         for( ia=5;ia<nv;ia++ )
        {
            psi[ia]= ql[ia];
        }
     }
      else
     {
         mp= 0;
         mm= m;
         psi[1]= qr[0];
         psi[2]= qr[1];
         psi[3]= qr[2];
         psi[4]= hr;
         for( ia=5;ia<nv;ia++ )
        {
            psi[ia]= qr[ia];
        }
     }
      ps= plp*pl+ prm*pr+ pa;
      //ps*= wc[3][ic];
      ps*= wn[3];

// assemble 
      f[0]= as*( mp*rl+ mm*rr );
      f[0]*= wn[3];
      f[1]= f[0]*psi[1]+ ps*wn[0];
      f[2]= f[0]*psi[2]+ ps*wn[1];
      f[3]= f[0]*psi[3]+ ps*wn[2];
      f[4]= f[0]*psi[4]+ ps*ugrid;
      for( ia=5;ia<nv;ia++ )
     {
          f[ia]= f[0]*psi[ia];
     }

     // as= fmax( as,fmax(al,ar) );
      *tmpauxc = (as+ fmax( fabs(unl),fabs(unr) ))*wn[3];
  }*/
