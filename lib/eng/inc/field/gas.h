#  ifndef _GAS_
#  define _GAS_

#  include <field/grad.h>
#  include <gas/janaf.h>

/* include <cosystem/cosystem.h>
   include <field/field.h>
   include <pickle/proto.h>*/
#  include <lin/vect.h>
#  include <field/field.h>
#  include <field/visc.h>


/**@ingroup engineering
  *@{
 **/

/**@defgroup gasdynamics Computational gasdynamics and Riemann solvers
  *@{
 **/


/** Minmod limiter 
   @param       r1            Left difference
   @param       r2            Right difference
 **/
   Real minmod( Real r1, Real r2 );

/** Gas type identifier
 **/
   enum gas_t{ bad_gas=-1, mfroe_gas, mfjanaf_gas, mfreacting_gas, mfausm_gas, mfausmup_gas, mfroeisoth_gas, roe_gas, acm_gas, heat_conduction_gas,mfroe_gas_cht,gas_num };

/** Gas type indentifying strings
 **/
   static const string gas_s[gas_num]= { "mfroe", "mfjanaf", "mfreacting", "mfausm", "mfausmup", "mfroeisoth", "roe", "acm", "heat_conduction", "mfroe_cht" };



/** Abstract gasdynamics and Riemann solver class */
   class cGas: public cField
  {

      protected:

         Int             nx,nvel,naux,nauxf,nlhs;
         Int             nv0,naux0,nauxf0,nlhs0;
         cCosystem      *coo;
         cVisc          *vsc;

/** Compute the lienearised changes in the primitive variables for given changes in the conserved variables. 3 velocity components.
   @param                   ist         starting index.
   @param                   ien         ending index.
   @param                   q           Primitive variables. q[iv][iq] is variable iq for degree of freedom iq.
   @param                   aux         Auxiliary variables. aux[iv][iq] is variable iv for degree of freedom iq.
   @param                   du          Changes in the conserved variables. u[iv][iq] is variable iv for degree of freedom iq. 
                                        The ordering of the first four conserved variables is "rho", "rho u", "rho v", "rho w", "rho e". 
                                        All other conserved variables appear in the same order as their primitive counterparts.
                                        See AU3X technical documentation.
   @param                   dq          Linearised changes  in the primitive variables.
                                        dq[iv][iq] is the linearised change for variable iv for degree of freedom iq.
 */
         virtual void       dvar3( Int ist, Int ien, Real *q[], Real *aux[], Real *du[], Real *dq[] ){};
         virtual void       dvar3( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq ){};
         virtual void       dvar3gpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq ){};
         virtual void       dvar3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq ){};


/** Compute auxiliary variables from the primitive variables. Auxiliary variables
    could be densities, speed of sound, enthalpies etc. 3 velocity components. DO NOT TRY to access these variables directly.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   q           Primitive variables. q[iv][iq] is variable iq for degree of freedom iq.
   @param                   aux         Auxiliary variables. aux[iv][iq] is variable iv for degree of freedom iq.
 */
         virtual void       auxv3( Int ist, Int ien, Real *q[], Real *aux[] ){};
         virtual void       auxv3( Int iqs, Int iqe, Real *q, Real *aux, Int nq ){};
         virtual void       auxv3gpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq ){};
         virtual void       auxv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, string arch ){};


/** Compute conserved variables from the primitive variables for the purpose of computing unsteady RHS. 
    3 velocity components. DO NOT TRY to access these variables directly.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   q           Primitive variables. q[iv][iq] is variable iq for degree of freedom iq.
   @param                   aux         Auxiliary variables. aux[iv][iq] is variable iv for degree of freedom iq.
   @param                   u           Conserved variables. u[iv][iq] is variable iv for degree of freedom iq. 
                                        The ordering of the first four conserved variables is "rho", "rho u", "rho v", "rho w", "rho e". 
                                        All other conserved variables appear in the same order as their primitive counterparts.
                                        See AU3X technical documentation.
 */
         virtual void       cnsv3( Int ist, Int ien, Real *q[], Real *aux[], Real *u[] ){};
         virtual void       cnsv3( Int iqs, Int iqe, Real *sq, Real *saux, Real *sqo, Int nq ){};
         virtual void       cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch ){};

         virtual void       csv_to_pri3( Int ist, Int ien, Real *qo[], Real *q[] ){};

         virtual void       cnsv3_z( Int ist, Int ien, Real *q[], Real *aux[], Real *u[] ){};


/** Inviscid wall flux in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. The left state is interpreted as the boundary.
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   rhsl        Right-hand side vector. Left state. rhsl[iv][iq] accumulates the fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. The left state is interpreted as the internal cell adjacent to 
                                        the boundary.
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   rhsr        Right-hand side vector. Right state. rhsr[iv][iq] accumulates the fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         //virtual void      wflx33( Int ist, Int ien, Int *icql, Real *ql[], Real *auxl[], Real *rhsl[],
         //                                            Int *icqr, Real *qr[], Real *auxr[], Real *rhsr[], 
         //                                                   Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void      wflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *srhsr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void      wflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

/** First order inviscid flux in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. 
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   rhsl        Right-hand side vector. Left state. rhsl[iv][iq] accumulates the fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. 
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   rhsr        Right-hand side vector. Right state. rhsr[iv][iq] accumulates the fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         virtual void      iflx33( Int ist, Int ien, Int *iql, Real *ql[], Real *auxl[], Real *rhsl[],
                                                     Int *iqr, Real *qr[], Real *auxr[], Real *rhsr[], 
                                                               Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void      iflx33( Int ics,Int ice, Int *sicql, Real *sql, Real *sauxl, Real *srhsl,
                                                    Int *sicqr, Real *sqr, Real *sauxr, Real *srhsr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void      iflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         virtual void      iflx33( Int ist, Int ien, Int *iq[2], Real *q[], Real *aux[], Real *rhs[],
                                   Real *wc[], Real *wxdc[], Real *auxc[] ){};

/**
         Computes second order MUSCL fluxes across a list of interfaces.
         @param                      ics              Starting index in face list
         @param                      ice              Ending index in face list
         @param                      icql             Pointers to left states. icql[ic] is the left state of interface ic.
         @param                      icqr             Pointers to right states. icqr[ic] is the right state of interface ic.
         @param                      idl              Dummy argument for the grad object. Left. Could be used, as an example, to indicate which family of lines is 
                                                      being used in a structured grid.
         @param                      idr              Dummy argument for the grad object. Right.Could be used, as an example, to indicate which family of lines is 
                                                      being used in a structured grid.
         @param                      xql              Positions of left states. If iql= icql[ic], then xql[0][iql], xql[1][iql], xql[2][iql] are the three coordinates of
                                                      the left state of interface ic.
         @param                      xqr              Positions of right states. If iqr= icqr[ic], then xqr[0][iqr], xqr[1][iqr], xqr[2][iqr] are the three coordinates of
                                                      the right state of interface ic.
         @param                      ql0              Flow variables of left states. If iql= icql[ic], then ql0[iv][iql] is the iv-th flow variable for the left state of
                                                      interface ic.
         @param                      qr0              Flow variables of right states.  If iqr= icqr[ic], then ql0[iv][iqr] is the iv-th flow variable for the right state of
                                                      interface ic.
         @param                      dxdl             Metric tensors of left states. If iql= icql[ic], then dxdl[ix][iql] is the ix-th component of the metric tensor for
                                                      the left state of interface ic. dxdl is not used directly by iflxmuscl33 but is passed to the deltq method of the grad 
                                                      object.
         @param                      dxdr             Metric tensors of right states. If iqr= icqr[ic], then dxdr[ix][iqr] is the ix-th component of the metric tensor for
                                                      the right state of interface ic. dxdl is not used directly by iflxmuscl33 but is passed to the deltq method of the 
                                                      grad object.
         @param                      dqdxl            Undivided differences of left states. If iql= icql[ic], then dqdxl[iv][iql] is the iv-th component of the undivided
                                                      difference tensor for the left state of interface ic. dqdxl is not used directly by iflxmuscl33 but is passed to the 
                                                      deltq method of the grad. 
         @param                      dqdxr            Undivided differences of right states. If iqr= icqr[ic], then dqdxr[iv][iqr] is the iv-th component of the undivided
                                                      difference tensor for the right state of interface ic. dqdxr is not used directly by iflxmuscl33 but is passed to the 
                                                      deltq method of the grad.
         @param                      auxl0            Auxiliary flow variables of left states. If iql= icql[ic], then auxl0[iv][iql] is the iv-th auxiliary flow variable 
                                                      for the left state of interface ic.
         @param                      auxr0            Auxiliary flow variables of right states. If iqr= icqr[ic], then auxr0[iv][iqr] is the iv-th auxiliary flow variable 
                                                      for the right state of interface ic.
         @param                      rhsl             Accumulated right hand sides for left states.If iql= icql[ic], then rhsl[iv][iql] is the iv-th right hand side 
                                                      for the left state of interface ic.
         @param                      rhsr             Accumulated right hand sides for right states.If iqr= icqr[ic], then rhsr[iv][iqr] is the iv-th right hand side 
                                                      for the right state of interface ic.
         @param                      xc               Position of the interface ic. xc[ix][ic] is the ix-th coordinate of interface ic.
         @param                      wc               Scaled normal of the interface ic. wc[ix][ic] is the ix-th component of the normal direction to interface ic. 
                                                      wc[3][ic] is the surface area of interface ic.
         @param                      wxdc             Integrated normal frame velocity to interface ic. wxdc[0][ic] is the integrall of the frame speed on interface ic.
         @param                      auxc             Auxiliray variables for interface ic. auxc[iv][ic] is the iv-th auxiliary variable for interface ic.
         @param                      grad             The gradient object. This is needed to perform the MUSCL extrapolation from the positions of the left and right state
                                                      onto the position of the interface.
 */
         virtual void iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *sxql, Real *sql, Real *sdxdxl, Real *sdqdxl, Real *sauxl, Real *srhsl,
                                                    Int *icqr, Int idr, Real *sxqr, Real *sqr, Real *sdxdxr, Real *sdqdxr, Real *sauxr, Real *srhsr,
                                                    Real  *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ){};
         virtual void iflxmuscl33( Int ics,Int ice, Int *sicq, Real *sxq, Real *sq0, Real *sdxdx, Real *sdqdx, Real *saux0, Real *srhs,
                                   Real  *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder ){};
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder ){};
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux0, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder ){};

/** Linearised inviscid wall flux in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. The left state is interpreted as the boundary.
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   dul         Changes in the conserved variables. Left state. dul[iv][iq] is the change in conserved variable iv for the left state iq.
   @param                   dql         Changes in the primitive variables. Left state. dql[iv][iq] is the change in primitive  variable iv for the left state iq.
   @param                   rhsl        Linearised right-hand side vector. Left state. drhsl[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. The left state is interpreted as the internal cell adjacent to 
                                        the boundary.
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   dur         Changes in the conserved variables. Right state. dur[iv][iq] is the change in conserved variable iv for the right state iq.
   @param                   dqr         Changes in the primitive variables. Right state. dqr[iv][iq] is the change in primitive  variable iv for the right state iq.
   @param                   drhsr       Linearised right-hand side vector. Right state. rhsr[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         //virtual void     dwflx33( Int ist,Int ien, Int *iql, Real *ql[], Real *auxl[], Real *dul[], Real *dql[], Real *drhsl[],
         //                                           Int *iqr, Real *qr[], Real *auxr[], Real *dur[], Real *dqr[], Real *drhsr[],
         //                                                     Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void     dwflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ){};
         virtual void     dwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

/** Linearised first order inviscid flux in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. 
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   dul         Changes in the conserved variables. Left state. dul[iv][iq] is the change in conserved variable iv for the left state iq.
   @param                   dql         Changes in the primitive variables. Left state. dql[iv][iq] is the change in primitive  variable iv for the left state iq.
   @param                   rhsl        Linearised right-hand side vector. Left state. drhsl[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. 
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   dur         Changes in the conserved variables. Right state. dur[iv][iq] is the change in conserved variable iv for the right state iq.
   @param                   dqr         Changes in the primitive variables. Right state. dqr[iv][iq] is the change in primitive  variable iv for the right state iq.
   @param                   drhsr       Linearised right-hand side vector. Right state. rhsr[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         //virtual void     diflx33( Int ist,Int ien, Int *iql, Real *ql[], Real *auxl[], Real *dul[], Real *dql[], Real *drhsl[],
         //                                           Int *iqr, Real *qr[], Real *auxr[], Real *dur[], Real *dqr[], Real *drhsr[],
         //                                                     Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void     diflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void     diflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         //virtual void     diflx33( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                          Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void     diflx33( Int ics,Int ice, Int *sicq, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                                   Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void     diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

         virtual void     diflxmuscl33( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                         Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                         Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd ) {};


/** Linearised injection/extraction flux in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. 
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   dul         Changes in the conserved variables. Left state. dul[iv][iq] is the change in conserved variable iv for the left state iq.
   @param                   dql         Changes in the primitive variables. Left state. dql[iv][iq] is the change in primitive  variable iv for the left state iq.
   @param                   rhsl        Linearised right-hand side vector. Left state. drhsl[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. 
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   dur         Changes in the conserved variables. Right state. dur[iv][iq] is the change in conserved variable iv for the right state iq.
   @param                   dqr         Changes in the primitive variables. Right state. dqr[iv][iq] is the change in primitive  variable iv for the right state iq.
   @param                   drhsr       Linearised right-hand side vector. Right state. rhsr[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         virtual void     djflx33( Int ist,Int ien, Int *iql, Real *ql[], Real *auxl[], Real *dul[], Real *dql[], Real *drhsl[],
                                                    Int *iqr, Real *qr[], Real *auxr[], Real *dur[], Real *dqr[], Real *drhsr[],
                                                              Real *wc[], Real *wxdc[], Real *auxc[] ){};

/** Linearised first order inviscid flux for free stream boundaries in three space dimensions, three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. 
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   dul         Changes in the conserved variables. Left state. dul[iv][iq] is the change in conserved variable iv for the left state iq.
   @param                   dql         Changes in the primitive variables. Left state. dql[iv][iq] is the change in primitive  variable iv for the left state iq.
   @param                   rhsl        Linearised right-hand side vector. Left state. drhsl[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. 
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   dur         Changes in the conserved variables. Right state. dur[iv][iq] is the change in conserved variable iv for the right state iq.
   @param                   dqr         Changes in the primitive variables. Right state. dqr[iv][iq] is the change in primitive  variable iv for the right state iq.
   @param                   drhsr       Linearised right-hand side vector. Right state. rhsr[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         //virtual void    diflxb33( Int ist,Int ien, Int *iql, Real *ql[], Real *auxl[], Real *dul[], Real *dql[], Real *drhsl[],
         //                                           Int *iqr, Real *qr[], Real *auxr[], Real *dur[], Real *dqr[], Real *drhsr[],
         //                                                     Real *wc[], Real *wxdc[], Real *auxc[] ){};
         virtual void diflxb33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                 Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                 Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq ){};
         virtual void diflxb33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};

/** Viscous fluxes. Three space dimensions and three velocity components.
   @param                   ist         starting index.
   @param                   ien         ending   index.
   @param                   iql         Face-dof connectivity. Left state. 
   @param                   ql          Primitive variables. Left state. ql[iv][iq] is primitive variable iv for the left state iq.
   @param                   auxl        Auxiliary variables. Left state. auxl[iv][iq] is auxiliary variable iv for the left state iq.
   @param                   dqdxl       Gradients of the primitive variables. Left state. dqdxl[ix][iv][iq] is d/dx_ix q[iv] evaluated at cell iq. 
   @param                   rhsl        Linearised right-hand side vector. Left state. drhsl[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
                                        left state iq.
   @param                   iqr         Face-dof connectivity. Right state. 
   @param                   qr          Primitive variables. Right state. qr[iv][iq] is primitive variable iv for the right state iq.
   @param                   auxr        Auxiliary variables. Right state. auxr[iv][iq] is auxiliary variable iv for the right state iq.
   @param                   drhsr       Linearised right-hand side vector. Right state. rhsr[iv][iq] accumulates the linearised fluxes for convserved variable iv for 
   @param                   dqdxr       Gradients of the primitive variables. Reft state. dqdxr[ix][iv][iq] is d/dx_ix q[iv] evaluated at cell iq. 
                                        right state iq.
   @param                   wc          Face normals. wc[ix][ic] is the ix component of the normal vector to the face. wc[3][ic] is the area of the 
                                        face.
   @param                   wxdc        Integral of the normal component of the frame speed over the face. Accessed as wxdc[0][ic].
   @param                   auxc        Auxiliary variables for the face. auxc[nauxc-1][ic] is the spectral radius of the flux jacobian of face ic.
 */
         //virtual void     mflx33( Int ist, Int ien, Int *iql, Real *xql[], Real *ql[], Real *auxl[], Real **dqdxl[], Real *rhsl[],
         //                                           Int *iqr, Real *xqr[], Real *qr[], Real *auxr[], Real **dqdxr[], Real *rhsr[], 
         //                                           Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void     mflx33( Int ist, Int ien, Int *iql, Real *sxql, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                                    Int *iqr, Real *sxqr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                                    Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void     mflx33( Int ist, Int ien, cAu3xView<Int>& iql,  cAu3xView<Real>& xql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& iqr,  cAu3xView<Real>& xqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void     mflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q[], Real *aux[], Real **dqdx0[], Real *rhs[],
         //                         Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void     mflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                                  Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void     mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void    dmflx33( Int , Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                     Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                            Real *[], Real *[], Real *[], Real *[] );
         virtual void     dmflx33( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                     Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                     Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void     dmflx33( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& sresl,
                                                     cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& sresr,
                                                     cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void    dmflx33( Int ics, Int ice, Int *icq[2], Real *x[], Real *q0[], Real *aux[], Real *dq0[], Real *daux[], Real *res[],
         //                         Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void    dmflx33( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                                  Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void    dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                                  cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );



         virtual void extrap33( Int ,Int , Int *, Real *[], Real *[], Int *, Real *[], Real *[],
                                                  Real *[], Real *[], Real *[], Real *[], Real *[] ){};

      public:

	//! Default constructor

         cGas(){ coo= NULL; vsc= NULL;};

	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cGas( cCosystem *, cVisc * );
         virtual void props(){};
         virtual gas_t gettype(){ return bad_gas; };
         cVisc *visc(){ return vsc; };

	//! get number of auxiliary variables
	/*!
	 * \return no return type
	 */
         virtual Int getnaux(){ return naux; };

	//! get number of variables
	/*!
	 * \return no return type
	 */
         virtual Int getnv(){ return nv; };

	//! get number of variables
	/*!
	 * \return no return type
	 */
         virtual Int getnv0(){ return nv0; };

	//! get number of flux auxiliary variables (Roe averages)
	/*!
	 * \return no return type
	 */
         virtual Int getnauxf(){ return nauxf; };

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getnlhs(){ return nlhs; };

	//! compute auxilary variables from main variables (main variables are  velocities, temperature, pressure)
	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param q[] an array of pointers to Real
	 * \param aux[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void auxv( Int , Int , Real *[], Real *[] );
         virtual void auxv( Int iqs, Int iqe, Real *q, Real *aux, Int nq );
         virtual void auxvgpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq );
         virtual void auxv( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, string arch );


	//! compute conservative variables from main variables and auxiliary variables
	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param q[] an array of pointers to Real
	 * \param aux[] an array of pointers to Real
	 * \param qo[] an array of pointers to Real
	 * \return type
	*/
         virtual void cnsv( Int , Int , Real *[], Real *[], Real *[] );
         virtual void cnsv( Int iqs, Int iqe, Real *sq, Real *saux, Real *sqo, Int nq );
         virtual void cnsv( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch );

         virtual void csv_to_pri( Int , Int , Real *[], Real *[] );

	//! compute perturbed conservative variables from main variables and auxiliary variables
	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param q[] an array of pointers to Real
	 * \param aux[] an array of pointers to Real
	 * \param qo[] an array of pointers to Real
	 * \return type
	*/
         virtual void cnsv_z( Int , Int , Real *[], Real *[], Real *[] );

	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param q[] an array of pointers to Real
	 * \param aux[] an array of pointers to Real
	 * \param dU[] an array of pointers to Real
	 * \param dq[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void dvar( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void dvar( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void dvargpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void dvar( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq );
         //virtual void qupd( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void qupd( Int iqs, Int iqe, Real *sq, Real *saux, Real *sdq, Real *sdaux, Int nq );
         virtual void qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux );

/**
         Computes first order inviscid fluxes across a list of interfaces.
         @param                      ics              Starting index in face list
         @param                      ice              Ending index in face list
         @param                      icql             Pointers to left states. icql[ic] is the left state of interface ic.
         @param                      icqr             Pointers to right states. icqr[ic] is the right state of interface ic.
         @param                      ql0              Flow variables of left states. If iql= icql[ic], then ql0[iv][iql] is the iv-th flow variable for the left state of
                                                      interface ic.
         @param                      qr0              Flow variables of right states.  If iqr= icqr[ic], then ql0[iv][iqr] is the iv-th flow variable for the right state of
                                                      interface ic.
         @param                      auxl0            Auxiliary flow variables of left states. If iql= icql[ic], then auxl0[iv][iql] is the iv-th auxiliary flow variable 
                                                      for the left state of interface ic.
         @param                      auxr0            Auxiliary flow variables of right states. If iqr= icqr[ic], then auxr0[iv][iqr] is the iv-th auxiliary flow variable 
                                                      for the right state of interface ic.
         @param                      rhsl             Accumulated right hand sides for left states.If iql= icql[ic], then rhsl[iv][iql] is the iv-th right hand side 
                                                      for the left state of interface ic.
         @param                      rhsr             Accumulated right hand sides for right states.If iqr= icqr[ic], then rhsr[iv][iqr] is the iv-th right hand side 
                                                      for the right state of interface ic.
         @param                      wc               Scaled normal of the interface ic. wc[ix][ic] is the ix-th component of the normal direction to interface ic. 
                                                      wc[3][ic] is the surface area of interface ic.
         @param                      wxdc             Integrated normal frame velocity to interface ic. wxdc[0][ic] is the integrall of the frame speed on interface ic.
         @param                      auxc             Auxiliray variables for interface ic. auxc[iv][ic] is the iv-th auxiliary variable for interface ic.
 */
         virtual void iflx( Int , Int , Int *, Real *[], Real *[], Real *[],
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[] );
         virtual void iflx( Int ics,Int ice, Int *sicql, Real *sql, Real *sauxl, Real *srhsl,
                                             Int *sicqr, Real *sqr, Real *sauxr, Real *srhsr,
                                             Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void iflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                             cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                             cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void iflx( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *rhs[],
                            Real *wc[], Real *wxdc[], Real *auxc[] );


/**
         Computes inject/extraction fluxes across a list of interfaces.
         @param                      ics              Starting index in face list
         @param                      ice              Ending index in face list
         @param                      icql             Pointers to left states. icql[ic] is the left state of interface ic.
         @param                      icqr             Pointers to right states. icqr[ic] is the right state of interface ic.
         @param                      ql0              Flow variables of left states. If iql= icql[ic], then ql0[iv][iql] is the iv-th flow variable for the left state of
                                                      interface ic.
         @param                      qr0              Flow variables of right states.  If iqr= icqr[ic], then ql0[iv][iqr] is the iv-th flow variable for the right state of
                                                      interface ic.
         @param                      auxl0            Auxiliary flow variables of left states. If iql= icql[ic], then auxl0[iv][iql] is the iv-th auxiliary flow variable 
                                                      for the left state of interface ic.
         @param                      auxr0            Auxiliary flow variables of right states. If iqr= icqr[ic], then auxr0[iv][iqr] is the iv-th auxiliary flow variable 
                                                      for the right state of interface ic.
         @param                      rhsl             Accumulated right hand sides for left states.If iql= icql[ic], then rhsl[iv][iql] is the iv-th right hand side 
                                                      for the left state of interface ic.
         @param                      rhsr             Accumulated right hand sides for right states.If iqr= icqr[ic], then rhsr[iv][iqr] is the iv-th right hand side 
                                                      for the right state of interface ic.
         @param                      wc               Scaled normal of the interface ic. wc[ix][ic] is the ix-th component of the normal direction to interface ic. 
                                                      wc[3][ic] is the surface area of interface ic.
         @param                      wxdc             Integrated normal frame velocity to interface ic. wxdc[0][ic] is the integrall of the frame speed on interface ic.
         @param                      auxc             Auxiliray variables for interface ic. auxc[iv][ic] is the iv-th auxiliary variable for interface ic.
 */

         //virtual void iflxmuscl( Int , Int , Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[],
         //                                    Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[], 
         //                                                Real *[], Real *[], Real *[], Real *[], cGrad * );
         virtual void iflxmuscl( Int ics,Int ice, Int *icql, Int idl, Real *sxql, Real *sql, Real *sdxdxl, Real *sdqdxl, Real *sauxl, Real *srhsl,
                                                  Int *icqr, Int idr, Real *sxqr, Real *sqr, Real *sdxdxr, Real *sdqdxr, Real *sauxr, Real *srhsr,
                                                  Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder );
         virtual void iflxmuscl( Int ics,Int ice, Int *sicq,  Real *sxq, Real *sq0, Real *sdxdx, Real *sdqdx, Real *saux0, Real *srhs,
                                 Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder );
         virtual void iflxmuscl( Int ics,Int ice, cAu3xView<Int>& icql, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                  cAu3xView<Int>& icqr, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         virtual void iflxmuscl( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux0, cAu3xView<Real>& rhs,
                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );


         virtual void ilhs( Int , Int , Int *, Real *[], Real *[], Real *[], 
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[], cJacBlk *jac_df[2] ){};
         virtual void ilhs( Int , Int , Int *, Real *[], Real *[], Real *[], 
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[] ){};
         virtual void ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
         virtual void ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs,          
                            Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) {};
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ) {};

         //virtual void diflx( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                      Real *[], Real *[], Real *[] );
         virtual void diflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                              Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void diflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& sql,   cAu3xView<Real>& sauxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& sqr,   cAu3xView<Real>& sauxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& swxdc, cAu3xView<Real>& sauxc );


         //virtual void diflx( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                    Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void diflx( Int ics,Int ice, Int *sicq, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                             Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) ;
         virtual void diflx( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                             cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflxmuscl( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                   Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                   Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd );


         //virtual void diflxb( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                       Real *[], Real *[], Real *[] );
         virtual void diflxb( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                               Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                               Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq );
         virtual void diflxb( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                               cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );


	/*!
	 * \param ics an Int
	 * \param ice an Int
	 * \param icql a pointer to Int
	 * \param ql an array of pointers to Real
	 * \param auxl[] an array of pointers to Real
	 * \param icqr a pointer to Int
	 * \param qr[] an array of pointers to Real
	 * \param auxr[] an array of pointers to Real
	 * \param wc[] an array of pointers to Real
	 * \param wxdc[] an array of pointers to Real
	 * \param wrkc[] an array of pointers to Real
	 * \param auxc[] an array of pointers to Real
	 * \return no return type
	 */
         //virtual void  wflx( Int , Int , Int *, Real *[], Real *[], Real *[],
         //                                Int *, Real *[], Real *[], Real *[], 
         //                                       Real *[], Real *[], Real *[] );
         virtual void    wflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,
                                                Int *icqr, Real *sqr, Real *sauxr, Real *srhsr,
                                                Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void    wflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void  dwflx( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                       Real *[], Real *[], Real *[] );
         virtual void     dwflx( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                  Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                  Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq );
         virtual void     dwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                  cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                  cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void  wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[], 
                                         Int *, Real *[], Real *[], Real *[], 
                                                Real *[], Real *[], Real *[], cJacBlk *jac_df[2] ){};
         //virtual void  wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[], 
         //                                Int *, Real *[], Real *[], Real *[], 
         //                                       Real *[], Real *[], Real *[] ){};
         virtual void wlhs( Int ics, Int ice, Int *icql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *icqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ){};
         virtual void wlhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc ){};
	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param iprbq a pointer to Int
	 * \param f a Real
	 * \param q[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void voffset( Int iqs, Int iqe, Real f, Int *isrc, Real *rhssrc[], Int *idst, Real *rhsdst[] );
         virtual void voffset( Int iqs, Int iqe, Real f, Int *isrc, Real *sqsrc, Int *idst, Real *sqdst, Int nqsrc, Int nqdst );
         virtual void voffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *sqsrc, Int *idst, Real *sqdst, Int nqsrc, Int nqdst );
         virtual void voffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& qsrc, cAu3xView<Int>& idst, cAu3xView<Real>& qdst );

         virtual void voffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *rhssrc_re[], Real *rhssrc_im[],
                                                                      Int *idst, Real *rhsdst_re[], Real *rhsdst_im[] );

	/*!
	 * \param ics an Int
	 * \param ice an Int
	 * \param f a Real
	 * \param wrk[] an array of pointers to Real
	 * \return no return type
	 */	
         virtual void roffset( Int iqs, Int iqe, Real f, Int *isrc, Real *rhssrc[], Int *idst, Real *rhsdst[] );
         virtual void roffset( Int iqs, Int iqe, Real f, Real *srhssrc, Int nqsrc );
         virtual void roffsetgpu( Int iqs, Int iqe, Real f, Real *srhssrc, Int nqsrc );
         virtual void roffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *srhs, Int *idst, Real *sdst, Int nqsrc, Int nqdst );
         virtual void roffset( Int iqs, Int iqe, Real f, cAu3xView<Real>& rhssrc );
         virtual void roffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& rhs, cAu3xView<Int>& idst, cAu3xView<Real>& dst );


         virtual void roffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *rhssrc_re[], Real *rhssrc_im[],
                                                                      Int *idst, Real *rhsdst_re[], Real *rhsdst_im[] );

         virtual void goffset( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *dxdxsrc[], Real **dqdxsrc[], 
                                                                      Int *idst, Real *dxdxdst[], Real **dqdxdst[] );
         virtual void goffset( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *sdxdxsrc, Real *sdqdxsrc,
                                                                      Int *idst, Real *sdxdxdst, Real *sdqdxdst,
                                                                      Int nqsrc, Int nqdst );
         virtual void goffsetgpu( Int iqs, Int iqe, Real f, Int *ijdx[], Int *isrc, Real *sdxdxsrc, Real *sdqdxsrc,
                                                                         Int *idst, Real *sdxdxdst, Real *sdqdxdst,
                                                                         Int nqsrc, Int nqdst );


         virtual void goffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *ijdx[], Int *isrc, Real *dxdxsrc[], Real **dqdxsrc_re[], Real **dqdxsrc_im[],
                                                                                   Int *idst, Real *dxdxdst[], Real **dqdxdst_re[], Real **dqdxdst_im[] );

         virtual void goffset( Int iqs, Int iqe, Real f, Int *isrc, Real **dqdxsrc[], 
                                                         Int *idst, Real **dqdxdst[] );
         virtual void goffset( Int iqs, Int iqe, Real f, Int *isrc, Real *sdqdxsrc,
                                                         Int *idst, Real *sdqdxdst,
                                                         Int nqsrc, Int nqdst );
         virtual void goffsetgpu( Int iqs, Int iqe, Real f, Int *isrc, Real *sdqdxsrc,
                                                            Int *idst, Real *sdqdxdst,
                                                            Int nqsrc, Int nqdst );
         virtual void goffset( Int iqs, Int iqe, Real f, Real *sdxdxsrc, Real *sdqdxsrc,Int nqsrc );

         virtual void goffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& dxdxsrc, cAu3xView<Real>& dqdxsrc,
                                                         cAu3xView<Int>& idst, cAu3xView<Real>& dxdxdst, cAu3xView<Real>& dqdxdst );
         virtual void goffset( Int iqs, Int iqe, Real f, cAu3xView<Int>& isrc, cAu3xView<Real>& dqdxsrc,
                                                         cAu3xView<Int>& idst, cAu3xView<Real>& dqdxdst );
         virtual void goffset( Int iqs, Int iqe, Real f, cAu3xView<Real>& dxdxsrc, cAu3xView<Real>& dqdxsrc );


         virtual void aoffset( Int iqs, Int iqe, Real f, Int *isrc, Real *lhsrc[], Int *idst, Real *lhdst[] ){};

         void offset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real *src_re[], Real *src_im[],         
                                                            Int *idst, Real *dst_re[], Real *dst_im[] );

         void goffset_z( Int iqs, Int iqe, Real f, Real ibpa, Int *isrc, Real **dqdxsrc_re[], Real **dqdxsrc_im[],
                                                              Int *idst, Real **dqdxdst_re[], Real **dqdxdst_im[] );


	/*!
	 * \param iqs an Int
	 * \param iqe an Int
	 * \param omega a Real
	 * \param wq[] an array of pointers to Real
	 * \param q[] an array of pointers to Real
	 * \param aux[] an array of pointers to Real
	 * \param xdq[] an array of pointers to Real
	 * \param rhs[] an array of pointers to Real
	 * \return no return type
	 */
         virtual void accel( Int , Int , Real ,Real *[], Real *[], Real *[], Real *[], Real *[], Real *[] );
         virtual void accel( Int iqs, Int iqe, Real omega, Real *swq, Real *sxq, Real *sq, Real *saux, Real *sxdq, Real *srhs, Int nq );
         virtual void accel( Int iqs, Int iqe, Real omega, cAu3xView<Real>& wq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& xdq, cAu3xView<Real>& rhs );

         virtual void daccel( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
                      Real *[], Real *[] );
         virtual void daccel( Int iqs, Int iqe, Real omega, Real *swq, Real *sxq, Real *sq, Real *sdq, Real *sdaux, Real *saux,       
                              Real *sxdq, Real *srhs, Int nq );
         virtual void daccel( Int iqs, Int iqe, Real omega, cAu3xView<Real>& wq, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& aux,
                              cAu3xView<Real>& xdq, cAu3xView<Real>& rhs );

         //virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq );
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa );
         //virtual void srhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Int *idst[], Int *igdst[], Real *[], Real *[], Real *[] );
         virtual void srhs( Int iqs,Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Int *sidst, Int *sigdst, Real *swq, Real *srhs, Real *slhsa, Int nq );
         virtual void srhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,cAu3xView<Real>& wq, cAu3xView<Real>& rhs, cAu3xView<Real>& lhsa );


         //virtual void dsrhs( Int , Int , Real , Real *[], Real *[], Real *[], Real *[], Real **[], Real *[], 
         //               Real *[], Real *[], Real *[] );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst,
                             Real *swq, Real *sres, Real *slhs, Int nq );
         virtual void dsrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                             cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs );
         virtual void vrhs( Int ist, Int ien, Real f, Real *u[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *rhs[], Real *lhsa[] );
         //virtual void dvrhs( Int ist, Int ien, Real f, Real *q[], Real *aux[], Real *dq[], Real *daux[], Real **dqdx[], Real *dst[], 
         //               Real *wq[], Real *drhs[], Real *lhs[] );
         virtual void dvrhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sdqdx, Real *sdst,        
                             Real *swq, Real *sres, Real *slhs, Int nq );
         virtual void dvrhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                             cAu3xView<Real>& wq, cAu3xView<Real>& res, cAu3xView<Real>& lhs );

/** Compute unsteady left hand side
   @param                      ist              Starting index in cell list
   @param                      ien              Ending index in cell list
   @param                     dtm               Physical time step
   @param                     cfl               Courant number for dual time stepping
                                                time derivatives
   @param                      q                Vector of primitive variables
   @param                    aux                Vector of auxiliaray variables
   @param                   dqdx                Gradients
   @param                    dst                Distances
   @param                     wq                Volumes
   @param                      lhs              LHS vector. This, in general, a block matrix. This function uses the
                                                last column which is the rescaled (local) time step.
 **/
         //virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[], Real rord ){};
         virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhs, Real rord, Int nq ){};
         virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord ){};

         //virtual void invdg( Int , Int , Real *[], Real *[] ){};
         virtual void invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq ){};
         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res ){};
         //virtual void fctdg( Int , Int , Real *[] ){};
         virtual void fctdg( Int , Int , Real * ){};
         virtual void fctdg( Int , Int , cAu3xView<Real>& ){};

         virtual void nondim( Int, Int, Real *[], Int * );
         virtual void nondim( Int, Int, cAu3xView<Real>& , Int * );
         virtual void redim( Int, Int, Real *[] );
         virtual void redim( Int, Int, cAu3xView<Real>& );
         virtual void redim_cpu( Int, Int, cAu3xView<Real>& );

         virtual void nondim_time( Real *dtm);
         virtual void rendim_time( Real *dtm);

         //virtual void maux( Int , Int , Real *[], Real *[], Real **[], Real *[] );
         virtual void maux( Int , Int , Real *[], Real *[], Real *[], Real **[], Real *[], Real );//, Int ng, Int *iqbq[MxNGrp][1], Real *auxb[MxNGrp][MxNVs], Int *igdst[1], Int *idst[1] );
         virtual void maux( Int iqs, Int iqe, Real *sxq, Real *sq, Real *sdst, Real *sdqdx, Real *saux, Real lmixmax, Int nq );
         virtual void maux( Int iqs, Int iqe, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dst, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux );

         //virtual void mflx( Int , Int , Int *, Real *[], Real *[], Real *[], Real **[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[], Real **[], Real *[], 
         //                                      Real *[], Real *[], Real *[], Real *[] );
         virtual void mflx( Int ist, Int ien, Int *iql, Real *sxql, Real *sql, Real *sauxl, Real *sdqdxl, Real *srhsl,
                                              Int *iqr, Real *sxqr, Real *sqr, Real *sauxr, Real *sdqdxr, Real *srhsr, 
                                              Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void mflx( Int ist, Int ien, cAu3xView<Int>& iql, cAu3xView<Real>& xql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
                                              cAu3xView<Int>& iqr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
                                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void mflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdqdx, Real *srhs,
                            Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void mflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& rhs,
                            cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void dmflx( Int , Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                         Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[], 
         //                                Real *[], Real *[], Real *[], Real *[] );
         virtual void   dmflx( Int ics, Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                 Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                 Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   dmflx( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void dmflx( Int ics, Int ice, Int *icq[2], Real *x[], Real *q0[], Real *aux[], Real *dq0[], Real *daux[], Real *res[],
         //                         Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void   dmflx( Int ics, Int ice, Int *sicq, Real *sx, Real *sq, Real *saux, Real *sdq, Real *sdaux,
                               Real *sres, Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void   dmflx( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux,
                               cAu3xView<Real>& res, cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void mwflx( Int , Int , Int *, Real *[], Real *[], Real *[], Real *[],
         //                                Int *, Real *[], Real *[], Real *[], Real *[], 
         //                                       Real *[], Real *[], Real *[] );
         virtual void   mwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *srhsl,         
                                                Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *srhsr,         
                                                Real *swc, Real *swxdc, Real *sauxc, Int nql, Int nqr );
         virtual void   mwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void  dmwflx( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                 Int *, Real *[], Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                        Real *[], Real *[], Real *[] );
         virtual void dmwflx( Int ics,Int ice, Int *icql, Real *sxl, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                               Int *icqr, Real *sxr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                               Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq );
         virtual void dmwflx( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& xl,   cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                               cAu3xView<Int>& icqr, cAu3xView<Real>& xr,   cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void extrap( Int ,Int , Int *, Real *[], Real *[], Int *, Real *[], Real *[],
                                                Real *[], Real *[], Real *[], Real *[], Real *[] );
         //virtual void   yplus( Int ist, Int ien, Int *iqd, Real *xq[], Real *q[], Real *aux[], Int *iqb, Real *qb[], Real *auxb[],
         //                      Real *dst[] );
         virtual void yplus( Int ist, Int ien, Int *siqd, Real *sxq, Real *sq, Real *saux, Int *siqb, Real *sqb, Real *sauxb,
                             Real *sdst, Int nbb, Int nq, Int ndst );
         virtual void yplus( Int ist, Int ien, cAu3xView<Int>& iqd, cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Int>& iqb, cAu3xView<Real>& qb, cAu3xView<Real>& auxb,
                             cAu3xView<Real>& dst );

        void dsflx( Int ics,Int ice, Int *icql, Real *xql[], Real *ql[], Real *auxl[], Real *zcl_re[], Real *zcl_im[], Real *zl_re[], Real *zl_im[], Real *rhsl[],
                                     Int *icqr, Real *xqr[], Real *qr[], Real *auxr[], Real *zcr_re[], Real *zcr_im[], Real *zr_re[], Real *zr_im[], Real *rhsr[],
                                     Real *xc[], Real *wc[], Real *wxdc[], bool bflag );

        virtual void dmwflx_z( Int ics,Int ice, Int *icql, Real *xl[], Real *ql[], Real *auxl[], Real *zl_re[], Real *zl_im[], Real *rhsl_re[], Real *rhsl_im[],
                                                Int *icqr, Real *xr[], Real *qr[], Real *auxr[], Real *zr_re[], Real *zr_im[], Real *rhsr_re[], Real *rhsr_im[],
                                                Real *wc[], Real *wxdc[], Real *auxc[] );

        virtual void set_acm_beta( Real var ) {};
        virtual void set_acm_nu( Real var ) {};

        virtual void set_solid_prop( Real rho, Real cp, Real kappa ) {};
  };

   class cMfRoeGas:public cGas
  {
      private:
      protected:
         Real            rg,gam;
         Real            eps;

         virtual void       dvar3( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void       dvar3( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void       dvar3gpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );
         virtual void       dvar3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dU, cAu3xView<Real>& dq );


         virtual void       auxv3( Int , Int , Real *[], Real *[] );
         virtual void       auxv3( Int iqs, Int iqe, Real *q, Real *aux, Int nq );
         virtual void       auxv3gpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq );
         virtual void       auxv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, string arch );

         virtual void       cnsv3( Int , Int , Real *[], Real *[], Real *[] );
         virtual void       cnsv3( Int iqs, Int iqe, Real *sq, Real *saux, Real *sqo, Int nq );
         virtual void       cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch );

         virtual void       cnsv3_z( Int , Int , Real *[], Real *[], Real *[] );
         virtual void       csv_to_pri3( Int , Int , Real *[], Real *[] );

         //virtual void      wflx33( Int , Int , Int *, Real *[], Real *[], Real *[],
         //                                      Int *, Real *[], Real *[], Real *[], 
         //                                             Real *[], Real *[], Real *[] );
         virtual void      wflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *srhsl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *srhsr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void      wflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void      iflx33( Int , Int , Int *, Real *[], Real *[], Real *[],
                                               Int *, Real *[], Real *[], Real *[], 
                                                      Real *[], Real *[], Real *[] );
         virtual void      iflx33( Int ics,Int ice, Int *sicql, Real *sql, Real *sauxl, Real *srhsl,
                                                    Int *sicqr, Real *sqr, Real *sauxr, Real *srhsr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void      iflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void      iflx33( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *rhs[],
                                   Real *wc[], Real *wxdc[], Real *auxc[] );

         virtual void iflxmuscl33( Int ics,Int ice, Int *icql, Int idl, Real *sxql, Real *sql, Real *sdxdxl, Real *sdqdxl, Real *sauxl, Real *srhsl,
                                                    Int *icqr, Int idr, Real *sxqr, Real *sqr, Real *sdxdxr, Real *sdqdxr, Real *sauxr, Real *srhsr,
                                                    Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder );
         virtual void iflxmuscl33( Int ics,Int ice, Int *sicq, Real *sxq, Real *sq0, Real *sdxdx, Real *sdqdx, Real *saux0, Real *srhs,
                                   Real *sxc, Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq, Int iorder );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux0, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         //virtual void     dwflx33( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                      Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                             Real *[], Real *[], Real *[] );
         virtual void     dwflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq );
         virtual void     dwflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void     diflx33( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                     Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                            Real *[], Real *[], Real *[] );
         virtual void     diflx33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                    Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                    Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void     diflx33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                    cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                    cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void     diflx33( Int ics,Int ice, Int *icq[2], Real *q[], Real *aux[], Real *dq[], Real *daux[], Real *res[],
         //                          Real *wc[], Real *wxdc[], Real *auxc[] );
         virtual void     diflx33( Int ics,Int ice, Int *sicq, Real *sq, Real *saux, Real *sdq, Real *sdaux, Real *sres,
                                   Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void     diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                                   cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         //virtual void    diflxb33( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                     Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
         //                                            Real *[], Real *[], Real *[] );
         virtual void diflxb33( Int ics,Int ice, Int *icql, Real *sql, Real *sauxl, Real *sdql, Real *sdauxl, Real *sresl,
                                                 Int *icqr, Real *sqr, Real *sauxr, Real *sdqr, Real *sdauxr, Real *sresr,
                                                 Real *swc, Real *swxdc, Real *sauxc, Int nbb, Int nq );
         virtual void diflxb33( Int ics,Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void     diflxmuscl33( Int ics,Int ice, Int idl, Int *icql, Real *xql[], Real *ql0[], Real *zl0[], Real *dxdxl[], Real **dqdxl[], Real **dzdxl[], Real *resl[],
                                                         Int idr, Int *icqr, Real *xqr[], Real *qr0[], Real *zr0[], Real *dxdxr[], Real **dqdxr[], Real **dzdxr[], Real *resr[],
                                                         Real *xc[], Real *wc[], Real *wxdc[], Real *auxc[], cGrad *grd );
      public:

	//! Default constructor
         cMfRoeGas(){ coo= NULL; eps= 0.05; };

         virtual gas_t gettype(){ return mfroe_gas; };
	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cMfRoeGas( cCosystem *, cVisc * );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

         virtual void ilhs( Int , Int , Int *, Real *[], Real *[], Real *[], 
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[] );
         virtual void ilhs( Int ics, Int ice, Int *sicql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *sicqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void ilhs( Int ics, Int ice, Int *sicq, Real *sq, Real *saux, Real *slhs,          
                            Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq ) ;
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         //virtual void wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[], 
         //                                      Real *[], Real *[], Real *[] );
         virtual void wlhs( Int ics, Int ice, Int *icql, Real *sql, Real *sauxl, Real *slhsl,
                                              Int *icqr, Real *sqr, Real *sauxr, Real *slhsr,
                                              Real *swc, Real *swxdc, Real *sauxc, Int nfc, Int nq );
         virtual void wlhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
/** Compute unsteady left hand side
   @param                      ist              Starting index in cell list
   @param                      ien              Ending index in cell list
   @param                     dtm               Physical time step
   @param                     cfl               Courant number for dual time stepping
                                                time derivatives
   @param                      q                Vector of primitive variables
   @param                    aux                Vector of auxiliaray variables
   @param                   dqdx                Gradients
   @param                    dst                Distances
   @param                     wq                Volumes
   @param                      lhs              LHS vector. This, in general, a block matrix. This function uses the
                                                last column which is the rescaled (local) time step.
 **/
         //virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[], Real rord );
         virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhs, Real rord, Int nq );
         virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord );
         //virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void slhs( Int iqs, Int iqe, Real cfl, Real *sq, Real *saux, Real *sdqdx, Real *sdst, Real *swq, Real *slhsa, Int nq );
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa );

         //virtual void invdg( Int , Int , Real *[], Real *[] );
         virtual void invdg( Int iqs, Int iqe, Real *slhs, Real *sres, Int nq );
         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res );

  };

   class cMfJanafGas:public cMfRoeGas, public cJanaf
  {
      protected:
         string      cpath;
         Real        xsp[MxNsp],ssp[MxNsp];
         virtual void  auxv2( Int , Int , Real *[], Real *[] );
         virtual void  auxv3( Int , Int , Real *[], Real *[] );
         virtual void  auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch );
         virtual void  dvar2( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void  dvar3( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void  dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view );

         virtual void iflx22( Int , Int , Int *, Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], 
                                                 Real *[], Real *[], Real *[] );
         virtual void iflx23( Int , Int , Int *, Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], 
                                                 Real *[], Real *[], Real *[] );
         virtual void iflx33( Int , Int , Int *, Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], 
                                                 Real *[], Real *[], Real *[] );
         virtual void iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl, 
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr, 
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflx22( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void diflx23( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void diflx33( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );


         virtual void diflxb22( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void diflxb23( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void diflxb33( Int ,Int , Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                          Int *, Real *[], Real *[], Real *[], Real *[], Real *[],
                                                 Real *[], Real *[], Real *[] );
         virtual void iflxmuscl22( Int , Int , Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[],
                                               Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[], 
                                                           Real *[], Real *[], Real *[], Real *[], cGrad * );
         virtual void iflxmuscl23( Int , Int , Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[],
                                               Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[], 
                                                           Real *[], Real *[], Real *[], Real *[], cGrad * );
         virtual void iflxmuscl33( Int , Int , Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[],
                                               Int *, Int, Real *[], Real *[], Real *[], Real **[], Real *[], Real *[], 
                                                           Real *[], Real *[], Real *[], Real *[], cGrad * );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );


      public:


	//! Default constructor
         cMfJanafGas(){ coo= NULL; eps= 0.05; };
         virtual gas_t gettype(){ return mfjanaf_gas; };

	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cMfJanafGas( cCosystem *, cVisc * );

         virtual void props(  );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

         virtual void offsetPath( string Path ){ cpath=Path; };

  };

   class cJanafGas:public cMfJanafGas
  {
      protected:
         Int             il1,il4,il3,ilm;
         Int             iha,ika,iaa;
         Int             ira,iea,iga;
         Int            *ija[MxNVs];
         string cpath;
         Real xsp[MxNsp],ssp[MxNsp];
      public:


	//! Default constructor
         cJanafGas(){ coo= NULL; eps= 0.05; };

	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cJanafGas( cCosystem *, cVisc * );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

         virtual void ilhs( Int , Int , Int *, Real *[], Real *[], Real *[], 
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[] );

         virtual void wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[], 
                                        Int *, Real *[], Real *[], Real *[], 
                                               Real *[], Real *[], Real *[] );

/** Compute unsteady left hand side
   @param                      ist              Starting index in cell list
   @param                      ien              Ending index in cell list
   @param                     dtm               Physical time step
   @param                     cfl               Courant number for dual time stepping
                                                time derivatives
   @param                      q                Vector of primitive variables
   @param                    aux                Vector of auxiliaray variables
   @param                   dqdx                Gradients
   @param                    dst                Distances
   @param                     wq                Volumes
   @param                      lhs              LHS vector. This, in general, a block matrix. This function uses the
                                                last column which is the rescaled (local) time step.
 **/
         virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[], Real rord );
         virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void invdg( Int , Int , Real *[], Real *[] );
         virtual void fctdg( Int , Int , Real *[] );
         virtual void aoffset( Int iqs, Int iqe, Real f, Int *isrc, Real *lhsrc[], Int *idst, Real *lhdst[] );

  };

   class cMfReactingGas:public cMfJanafGas
  {
      protected:
         string      cpath;
         Int         nrc;
         Real        zrc[5],xsp[5][MxNsp],ssp[5][MxNsp];

         #pragma acc routine seq
         inline void gtcomp( Real, Real * );

         virtual void auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch );
         virtual void auxv3gpu( Int iqs, Int iqe, Real *q, Real *aux, Int nq );

         virtual void dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view );
         virtual void dvar3gpu( Int iqs, Int iqe, Real *q, Real *aux, Real *dU, Real *dq, Int nq );

         virtual void iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );


         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflxb33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void     mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void     mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                                  cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

      public:


	//! Default constructor
         cMfReactingGas(){ coo= NULL; eps= 0.05; };
         virtual gas_t gettype(){ return mfreacting_gas; };

	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cMfReactingGas( cCosystem *, cVisc * );

         virtual void props(  );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

         virtual void offsetPath( string Path ){ cpath=Path; };

         virtual void qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux );
  };

   class cHeatConduction: public cGas
  {
      private:
      protected:

         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                            cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                            cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord );
         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res );
         virtual void dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view );
         virtual void auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch );
         virtual void cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch );
         virtual void mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );


      public:

	//! Default constructor
         cHeatConduction(){ };

         virtual gas_t gettype(){ return heat_conduction_gas; };
	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cHeatConduction( cCosystem *, cVisc * );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

  };

   class cMfRoeGasCHT:public cMfRoeGas
  {
      private:
         Real solid_rho, solid_cp, solid_kappa;
      protected:

         virtual void dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view );
         virtual void auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch );
         virtual void cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch );
         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void mflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx0, cAu3xView<Real>& rhs,
                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
//         virtual void mflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl0, cAu3xView<Real>& rhsl,
//                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr0, cAu3xView<Real>& rhsr,
//                                                cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
//         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& xl, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
//                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& xr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
//                               cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void dmflx33( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& x, cAu3xView<Real>& q0, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );
         virtual void wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         void iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                       cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                       cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );


      public:

	//! Default constructor
         cMfRoeGasCHT(){ coo= NULL; eps= 0.05; };

         virtual gas_t gettype(){ return mfroe_gas_cht; };
	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cMfRoeGasCHT( cCosystem *, cVisc * );

         //void qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux );
//         virtual void mflx( Int ist, Int ien, cAu3xView<Int>& iql, cAu3xView<Real>& xql, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& rhsl,
//                                              cAu3xView<Int>& iqr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& rhsr,
//                                              cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void qupd( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq, cAu3xView<Real>& daux );

         virtual void set_solid_prop( Real rho, Real cp, Real kappa ) { solid_rho = rho; solid_cp = cp, solid_kappa = kappa;};
  };

   class cMfAusmUpGas:public cGas
  {
      private:
      protected:
         Real            rg,gam;
         Real            alpha0,beta0;
         Real            minf,sigma0,kp,ku,delt0;

         //virtual void       dvar2( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         //virtual void       dvar3( Int , Int , Real *[], Real *[], Real *[], Real *[] );
         virtual void dvar3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, cAu3xView<Real>& dU_view, cAu3xView<Real>& dq_view );


         //virtual void       auxv2( Int , Int , Real *[], Real *[] );
         //virtual void       auxv3( Int , Int , Real *[], Real *[] );
         virtual void auxv3( Int iqs, Int iqe, cAu3xView<Real>& q_view, cAu3xView<Real>& aux_view, string arch );


         //virtual void       cnsv2( Int , Int , Real *[], Real *[], Real *[] );
         //virtual void       cnsv3( Int , Int , Real *[], Real *[], Real *[] );
         virtual void cnsv3( Int iqs, Int iqe, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& qo, string arch );

         virtual void wflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );


         virtual void iflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& rhsl,
                                               cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& rhsr,
                                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icq,  cAu3xView<Real>& xq, cAu3xView<Real>& q, cAu3xView<Real>& dxdx, cAu3xView<Real>& dqdx, cAu3xView<Real>& aux, cAu3xView<Real>& rhs,
                                   cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder ) ;

         virtual void iflxmuscl33( Int ics,Int ice, cAu3xView<Int>& icql_view, Int idl, cAu3xView<Real>& xql, cAu3xView<Real>& ql0, cAu3xView<Real>& dxdxl, cAu3xView<Real>& dqdxl, cAu3xView<Real>& auxl0, cAu3xView<Real>& rhsl,
                                                    cAu3xView<Int>& icqr_view, Int idr, cAu3xView<Real>& xqr, cAu3xView<Real>& qr0, cAu3xView<Real>& dxdxr, cAu3xView<Real>& dqdxr, cAu3xView<Real>& auxr0, cAu3xView<Real>& rhsr,
                                                    cAu3xView<Real>& xc, cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc, Int iorder );

         virtual void dwflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflxb33( Int ics,Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql, cAu3xView<Real>& auxl, cAu3xView<Real>& dql, cAu3xView<Real>& dauxl, cAu3xView<Real>& resl,
                                                 cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr, cAu3xView<Real>& auxr, cAu3xView<Real>& dqr, cAu3xView<Real>& dauxr, cAu3xView<Real>& resr,
                                                 cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void diflx33( Int ics,Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dq0, cAu3xView<Real>& daux, cAu3xView<Real>& res,
                               cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

         virtual void ausm_plus_up_flux( Real *ql, Real *auxl, Real *qr, Real *auxr, Real *wn, Real ugrid, Real *f, Real *tmpauxc );

      public:

	//! Default constructor
         cMfAusmUpGas(){ coo= NULL; };
         virtual gas_t gettype(){ return mfausmup_gas; };

	//! Construct from a pointer to cCosystem
	/*!
	 * \param Coo a pointer to cCosystem
	 */
         cMfAusmUpGas( cCosystem *, cVisc * );

	//! get number of LHS variables
	/*!
	 * \return no return type
	 */
         virtual Int getNlhs(){ return nlhs; };

         //virtual void ilhs( Int , Int , Int *, Real *[], Real *[], Real *[], 
         //                               Int *, Real *[], Real *[], Real *[],
         //                                      Real *[], Real *[], Real *[] );
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icql, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc,  cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );
         virtual void ilhs( Int ics, Int ice, cAu3xView<Int>& icq, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& lhs,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );



         //virtual void wlhs( Int , Int,  Int *, Real *[], Real *[], Real *[],
         //                               Int *, Real *[], Real *[], Real *[],
         //                                      Real *[], Real *[], Real *[] );
         virtual void wlhs( Int ics, Int ice, cAu3xView<Int>& icql_view, cAu3xView<Real>& ql,   cAu3xView<Real>& auxl, cAu3xView<Real>& lhsl,
                                              cAu3xView<Int>& icqr_view, cAu3xView<Real>& qr,   cAu3xView<Real>& auxr, cAu3xView<Real>& lhsr,
                                              cAu3xView<Real>& wc, cAu3xView<Real>& wxdc, cAu3xView<Real>& auxc );

/** Compute unsteady left hand side
   @param                      ist              Starting index in cell list
   @param                      ien              Ending index in cell list
   @param                     dtm               Physical time step
   @param                     cfl               Courant number for dual time stepping
                                                time derivatives
   @param                      q                Vector of primitive variables
   @param                    aux                Vector of auxiliaray variables
   @param                   dqdx                Gradients
   @param                    dst                Distances
   @param                     wq                Volumes
   @param                      lhs              LHS vector. This, in general, a block matrix. This function uses the
                                                last column which is the rescaled (local) time step.
 **/
         //virtual void vlhs( Int ist, Int ien, Real dtm, Real cfl, Real *q[], Real *aux[], Real **dqdx[], Real *dst[], Real *wq[], Real *lhs[], Real rord );
         virtual void vlhs( Int iqs, Int iqe, Real dtm,Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst,
                            cAu3xView<Real>& wq, cAu3xView<Real>& lhs, Real rord );

//         virtual void slhs( Int , Int , Real , Real *[], Real *[], Real **[], Real *[], Real *[], Real *[] );
         virtual void slhs( Int iqs, Int iqe, Real cfl, cAu3xView<Real>& q, cAu3xView<Real>& aux, cAu3xView<Real>& dqdx, cAu3xView<Real>& dst, cAu3xView<Real>& wq, cAu3xView<Real>& lhsa );

         virtual void invdg( Int iqs, Int iqe, cAu3xView<Real>& lhs, cAu3xView<Real>& res );

         //virtual void invdg( Int , Int , Real *[], Real *[] );
  };

   cGas *newgas( Int, cCosystem *, cVisc * );

/** 
  *@}
 **/
/** 
  *@}
 **/

#endif

